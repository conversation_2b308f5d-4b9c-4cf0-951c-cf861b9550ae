{"name": "bbw-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.7", "@heroicons/react": "v1", "@next/font": "13.1.0", "@supabase/supabase-js": "2", "@tailwindcss/forms": "^0.5.3", "autoprefixer": "^10.4.13", "axios": "^1.9.0", "dayjs": "^1.11.10", "eslint": "8.30.0", "eslint-config-next": "13.1.0", "next": "^13.1.0", "postcss": "^8.4.20", "react": "18.2.0", "react-color": "^2.19.3", "react-dom": "18.2.0", "react-icons": "^4.7.1", "react-json-to-csv": "^1.2.0", "react-supabase": "^0.2.0", "react-tailwindcss-datepicker": "^1.6.6", "short-uuid": "^4.2.2", "slugify": "^1.6.6", "svg-loaders-react": "^2.2.1", "tailwindcss": "^3.2.4"}}