import { useLayoutEffect, useState, useEffect, useRef, Fragment } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import AddUserMenu from "@components/ui/AddUserMenu";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";
import Link from "next/link";
import CsvDownloadButton from "react-json-to-csv";

import { Dialog, Transition } from "@headlessui/react";
import { ExclamationCircleIcon } from "@heroicons/react/outline";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const Stories = () => {
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [totalScreenings, setTotalScreenings] = useState(null);

  const checkbox = useRef();
  const [checked, setChecked] = useState(false);

  const [indeterminate, setIndeterminate] = useState(false);
  const [selectedPeople, setSelectedPeople] = useState([]);

  const [open, setOpen] = useState(false);
  const [storyDeleteId, setStoryDeleteId] = useState(null);
  const [storyDeleteName, setStoryDeleteName] = useState(null);

  const cancelButtonRef = useRef(null);

  /* --- DEBUG --- */

  // console.log(session);

  // console.log(userData);

  // console.log(responseJson);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    getStories();
  }, [session]);

  async function getStories() {
    try {
      setLoading(true);
      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("stories")
        .select("*")
        .order("name", { ascending: true });

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // console.log(data);
        // console.log(data.length);
        setTotalScreenings(data.length);
        setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  const deleteStory = (id, name) => {
    setOpen(true);
    setStoryDeleteId(id);
    setStoryDeleteName(name);
  };

  const deleteUserConfirm = (id) => {
    setOpen(false);
    deleteStoryDB(storyDeleteId);
  };

  async function deleteStoryDB(id) {
    const userDataToSend = {
      entry: id,
    };
    try {
      setLoading(true);

      let { error } = await supabase.from("stories").delete().match({ id: id });

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      getStories(); // reload the data to remove from table
      setLoading(false);
    }
  }

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <div>
              <LoginForm />
            </div>
          ) : (
            <>
              {userData && userData.length == 0 ? (
                <NotAuthorised />
              ) : (
                <div className="min-h-full">
                  <Header />

                  <Transition.Root show={open} as={Fragment}>
                    <Dialog
                      as="div"
                      className="relative z-10"
                      initialFocus={cancelButtonRef}
                      onClose={setOpen}
                    >
                      <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                      >
                        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                      </Transition.Child>

                      <div className="fixed inset-0 z-10 overflow-y-auto">
                        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                          <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                          >
                            <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                              <div className="sm:flex sm:items-start">
                                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                  <ExclamationCircleIcon
                                    className="h-6 w-6 text-red-600"
                                    aria-hidden="true"
                                  />
                                </div>
                                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                  <Dialog.Title
                                    as="h3"
                                    className="text-lg font-medium leading-6 text-gray-900"
                                  >
                                    Delete story
                                  </Dialog.Title>
                                  <div className="mt-2">
                                    <p className="text-sm text-gray-500">
                                      Are you sure you want to delete the story{" "}
                                      {storyDeleteName}?
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                                <button
                                  type="button"
                                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                                  onClick={() => deleteUserConfirm()}
                                >
                                  Delete
                                </button>
                                <button
                                  type="button"
                                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                                  onClick={() => setOpen(false)}
                                  ref={cancelButtonRef}
                                >
                                  Cancel
                                </button>
                              </div>
                            </Dialog.Panel>
                          </Transition.Child>
                        </div>
                      </div>
                    </Dialog>
                  </Transition.Root>
                  <main>
                    <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                      <div className="px-4 sm:px-6 lg:px-8">
                        <div className="sm:flex sm:items-center">
                          <div className="sm:flex-auto">
                            <h1 className="text-xl font-semibold text-gray-900 pt-1">
                              Stories
                            </h1>
                            {/* Help text */}
                            {/* <p className="mt-2 text-sm text-gray-700">
                            Filter and select users, results are displayed in
                            the table below
                          </p> */}
                          </div>
                          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                            {/* <AddUserMenu /> */}

                            <Link href="/addstory">
                              <div className=" ml-3 inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">
                                Add new story
                              </div>
                            </Link>

                            {/* <div className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">
                              <CsvDownloadButton data={userData && userData} />
                            </div>
                            <div className="inline-flex items-center justify-center border border-transparent bg-gray-400 px-4 py-2 text-sm font-medium text-white shadow-sm ml-3">
                              Total screenings: {totalScreenings}
                            </div> */}
                          </div>
                        </div>

                        {/* Filter */}

                        <div className="mt-8 flex flex-col">
                          <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-12">
                            <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                              <div className="relative overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                                <table className="min-w-full table-fixed divide-y divide-gray-300">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        Image
                                      </th>
                                      <th
                                        scope="col"
                                        // className="min-w-[12rem] py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"
                                        className="px-5 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        Title
                                      </th>

                                      <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        <span className="sr-only">Edit</span>
                                      </th>
                                      <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        <span className="sr-only">Delete</span>
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody className="divide-y divide-gray-200 bg-white">
                                    {userData &&
                                      userData.map((story) => (
                                        <tr
                                          key={story.id}
                                          // className={
                                          //   selectedPeople.includes(person)
                                          //     ? "bg-gray-50"
                                          //     : undefined
                                          // }
                                        >
                                          <td className="whitespace-nowrap px-5 py-4 text-sm text-gray-500">
                                            <img
                                              className="w-24 rounded-md border border-1"
                                              src={`data:image/jpeg;base64,${story.thumbnail}`}
                                            />
                                          </td>
                                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                            {story.name}
                                          </td>

                                          <td className="whitespace-nowrap py-4 pr-4 text-right text-sm font-medium sm:pr-2">
                                            <Link
                                              href={"/editstory?id=" + story.id}
                                              className="text-primary hover:text-indigo-900"
                                            >
                                              Edit
                                              <span className="sr-only">
                                                , {story.name}
                                              </span>
                                            </Link>
                                          </td>
                                          <td className="whitespace-nowrap py-4 text-right text-sm font-medium sm:pr-6">
                                            <button
                                              onClick={() =>
                                                deleteStory(
                                                  story.id,
                                                  story.name
                                                )
                                              }
                                              className="text-red-600 hover:text-indigo-900"
                                            >
                                              Delete
                                            </button>
                                          </td>
                                        </tr>
                                      ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* <div className="px-4 py-4 sm:px-0">
                    <div className="h-96 rounded-lg border-4 border-dashed border-gray-200" />
                  </div> */}
                      {/* /End replace */}
                    </div>
                  </main>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default Stories;
