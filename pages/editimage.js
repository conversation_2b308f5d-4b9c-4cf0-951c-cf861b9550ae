import { useLayoutEffect, useState, useEffect, useRef } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";
import Link from "next/link";
import { useRouter } from "next/router";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const EditImage = () => {
  const router = useRouter();
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);

  //   const checkbox = useRef();
  //   const [checked, setChecked] = useState(false);

  //   const [indeterminate, setIndeterminate] = useState(false);
  //   const [selectedPeople, setSelectedPeople] = useState([]);

  const [imageId, setImageId] = useState([]);
  const [imageString, setImageString] = useState([]);
  const [imageInfo, setImageInfo] = useState([]);
  const [imageName, setImageName] = useState([]);
  const [imageIndex, setImageIndex] = useState([]);

  //   const [Name, setName] = useState([]);
  //   const [bookShopLink, setBookshopLink] = useState([]);
  //   const [freeBook, setFreeBook] = useState([]);
  //   const [storyImagesIds, setStoryImagesIds] = useState([]);
  //   const [storyImages, setStoryImages] = useState([]);
  const [userCreated, setUserCreated] = useState(false);

  const [errorOnSubmit, setErrorOnSubmit] = useState(false);

  const { query } = useRouter();

  const storyRef = query.id;

  /* --- DEBUG --- */

  //   console.log("imageString");
  //   console.log(imageString);

  //   console.log("storyImages");
  //   console.log(storyImages);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    if (query && query.id) {
      getImage(query && query.imgId);
    }
  }, [query]);

  async function updateImage() {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("images")
        .update({
          name: imageName.entry,
          image_string: imageString.entry,
          info: imageInfo.entry,
          image_id: imageIndex.entry,
        })
        .eq("id", imageId);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // console.log(data);
        //   setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getImage(id) {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("images")
        .select("*")
        .eq("id", id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // console.log(data);
        setImageId(data.id);
        setImageName({
          entry: data.name,
        });
        setImageInfo({
          entry: data.info,
        });
        setImageString({
          entry: data.image_string,
        });
        setImageIndex({
          entry: data.image_id,
        });
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  // update currSelection when entry changes
  const updateSelection = (e) => {
    const { value: value } = e.target;
    const type = e.target.id;

    // if (e.target.value !== "") {
    switch (type) {
      case "imageId":
        setImageId({
          entry: value,
          type: type,
        });

        return;

      case "imageString":
        setImageString({
          entry: value,
          type: type,
        });

        return;

      case "imageInfo":
        setImageInfo({
          entry: value,
          type: type,
        });

        return;

      case "imageName":
        setImageName({
          entry: value,
          type: type,
        });

        return;

      case "imageIndex":
        setImageIndex({
          entry: value,
          type: type,
        });

        return;

      default:
        return;
    }
    // }
  };

  //   const resetAddAdminUser = () => {
  //     setUserCreated(false);
  //     setName([]);
  //     setEmail([]);
  //   };

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <div>
              <LoginForm />
            </div>
          ) : (
            <>
              {userData && userData.length == 0 ? (
                <NotAuthorised />
              ) : (
                <div className="min-h-full">
                  <Header />

                  <main>
                    <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                      <div className="sm:flex sm:items-center">
                        <div className="sm:flex-auto">
                          <h1 className="text-xl font-semibold text-gray-900 pt-1 pb-6">
                            Edit image
                          </h1>
                          {/* Help text */}
                          {/* <p className="mt-2 text-sm text-gray-700">
                          Filter and select users, results are displayed in
                          the table below
                        </p> */}
                        </div>
                        {/* <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <button
                          onClick={invokeFunction}
                          // onClick={addUser}
                          type="button"
                          className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                          Add user
                        </button>
                      </div> */}
                        {errorOnSubmit ? (
                          <div className="text-red-500">{errorOnSubmit}</div>
                        ) : null}
                      </div>

                      <div className="mt-0 bg-white rounded-b-lg shadow-sm mb-3 pb-6">
                        <>
                          {!userCreated ? (
                            <div className="space-y-8 divide-y divide-gray-200">
                              <div className="space-y-8 divide-y divide-gray-200 ">
                                <div className="space-y-6 pt-8 sm:space-y-5">
                                  <div>
                                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                                      Image details
                                    </h3>
                                    {/* <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                  Use a permanent address where you can
                                  receive mail.
                                </p> */}
                                  </div>
                                  <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                    <label
                                      htmlFor="Name"
                                      className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                    >
                                      Image name
                                    </label>
                                    <div className="mt-1 sm:col-span-2 sm:mt-0">
                                      <input
                                        value={
                                          (imageName && imageName.entry) || ""
                                        }
                                        onChange={updateSelection}
                                        type="text"
                                        name="imageName"
                                        id="imageName"
                                        className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                      />
                                    </div>
                                  </div>

                                  <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                    <label
                                      htmlFor="Name"
                                      className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                    >
                                      Image preview
                                    </label>
                                    <div className="mt-1 sm:col-span-2 sm:mt-0">
                                      <img
                                        className="w-24 rounded-md border border-1"
                                        src={`data:image/jpeg;base64,${
                                          imageString && imageString.entry
                                        }`}
                                      />
                                    </div>
                                  </div>
                                  <div className="space-y-6 sm:space-y-5">
                                    <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="Name"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Image string
                                      </label>
                                      <div className="mt-1 sm:col-span-2 sm:mt-0">
                                        <textarea
                                          type="text"
                                          id="imageString"
                                          name="imageString"
                                          onChange={updateSelection}
                                          // onChange={(e) => setLifeMoto(e.target.value)}
                                          rows={3}
                                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                          placeholder="image string base64"
                                          defaultValue={
                                            (imageString &&
                                              imageString.entry) ||
                                            ""
                                          }
                                        />
                                      </div>
                                    </div>
                                    <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="Name"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Image text
                                      </label>
                                      <div className="mt-1 sm:col-span-2 sm:mt-0">
                                        <textarea
                                          type="text"
                                          id="imageInfo"
                                          name="imageInfo"
                                          onChange={updateSelection}
                                          rows={3}
                                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                          placeholder="image text"
                                          defaultValue={
                                            (imageInfo && imageInfo.entry) || ""
                                          }
                                        />
                                      </div>
                                    </div>

                                    <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="Name"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Image index
                                      </label>
                                      <div className="mt-1 sm:col-span-2 sm:mt-0">
                                        <input
                                          value={
                                            (imageIndex && imageIndex.entry) ||
                                            ""
                                          }
                                          onChange={updateSelection}
                                          type="text"
                                          name="imageIndex"
                                          id="imageIndex"
                                          className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="pt-5">
                                <div className="flex justify-end">
                                  <Link href={"/editstory?id=" + storyRef}>
                                    <div className="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                      Cancel
                                    </div>
                                  </Link>
                                  <button
                                    onClick={updateImage}
                                    className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                  >
                                    Update
                                  </button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div>
                              <div className="">
                                <h2 className="text-md text-gray-900">
                                  User created
                                </h2>

                                <div className="flex pt-3">
                                  <div className="pr-3">
                                    <button
                                      type="button"
                                      onClick={resetAddAdminUser}
                                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-secondaryPurpleA focus:outline-none"
                                    >
                                      Add another user
                                    </button>
                                  </div>
                                  <div>
                                    <Link href="/">
                                      <div
                                        type="button"
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-secondaryPurpleA focus:outline-none"
                                      >
                                        Finished
                                      </div>
                                    </Link>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      </div>
                    </div>
                  </main>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default EditImage;
