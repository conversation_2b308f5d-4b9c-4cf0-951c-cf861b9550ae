import { useLayoutEffect, useState, useEffect, useRef } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";
import Link from "next/link";
import { useRouter } from "next/router";
import Datepicker from "react-tailwindcss-datepicker";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const AddAdminUser = () => {
  const router = useRouter();
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);

  const checkbox = useRef();
  const [checked, setChecked] = useState(false);

  const [indeterminate, setIndeterminate] = useState(false);
  const [selectedPeople, setSelectedPeople] = useState([]);

  const [Name, setName] = useState([]);
  const [email, setEmail] = useState([]);
  const [userId, setUserId] = useState([]);
  const [userExpiry, setUserExpiry] = useState([]);
  // const [userNewExpiry, setUserNewExpiry] = useState([]);

  const [userCreated, setUserCreated] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState(false);

  const { query } = useRouter();

  // const [value, setValue] = useState({
  //   startDate: null,
  //   endDate: null,
  // });

  /* --- DEBUG --- */

  // console.log("userNewExpiry");
  // console.log(userNewExpiry.endDate);

  // console.log("userExp");
  // console.log(userExpiry);

  // console.log(Name);
  // console.log(email);
  // console.log(userId);

  // console.log(query);

  // console.log(session);

  //   console.log(responseJson);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    if (query && query.id) {
      getUser(query && query.id);
    }
  }, [query]);

  async function updateProfile() {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("profiles")
        .update({
          email: email.entry,
          white_label: userExpiry.endDate,
        })
        .eq("id", userId);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getUser(id) {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // console.log(data);

        setUserId(data.id);

        setEmail({
          entry: data.email,
        });

        // setName({
        //   entry: data.name,
        // });

        setUserExpiry({
          startDate: data.white_label,
          endDate: data.white_label,
        });
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  // update currSelection when entry changes
  const updateSelection = (e) => {
    const { value: value } = e.target;
    const type = e.target.id;

    // if (e.target.value !== "") {
    switch (type) {
      case "email":
        setEmail({
          entry: value,
          type: type,
        });
        // setIsValid(true);
        return;

      case "Name":
        setName({
          entry: value,
          type: type,
        });

        return;

      default:
        return;
    }
    // }
  };

  const resetAddAdminUser = () => {
    setUserCreated(false);
    setName([]);
    setEmail([]);
  };

  const handleValueChangeDate = (newValue) => {
    setUserExpiry(newValue);
  };

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <div>
              <LoginForm />
            </div>
          ) : (
            <>
              {userData && userData.length == 0 ? (
                <NotAuthorised />
              ) : (
                <div className="min-h-full">
                  <Header />

                  <main>
                    <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                      <div className="sm:flex sm:items-center">
                        <div className="sm:flex-auto">
                          <h1 className="text-xl font-semibold text-gray-900 pt-1 pb-6">
                            Edit user
                          </h1>
                          {/* Help text */}
                          {/* <p className="mt-2 text-sm text-gray-700">
                          Filter and select users, results are displayed in
                          the table below
                        </p> */}
                        </div>
                        {/* <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <button
                          onClick={invokeFunction}
                          // onClick={addUser}
                          type="button"
                          className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                          Add user
                        </button>
                      </div> */}
                        {errorOnSubmit ? (
                          <div className="text-red-500">{errorOnSubmit}</div>
                        ) : null}
                      </div>

                      <div className="mt-0 bg-white rounded-b-lg shadow-sm mb-3 pb-6">
                        <>
                          {!userCreated ? (
                            <div className="space-y-8 divide-y divide-gray-200">
                              <div className="space-y-8 divide-y divide-gray-200 ">
                                <div className="space-y-6 pt-8 sm:space-y-5">
                                  <div>
                                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                                      User details
                                    </h3>
                                    {/* <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                  Use a permanent address where you can
                                  receive mail.
                                </p> */}
                                  </div>
                                  <div className="space-y-6 sm:space-y-5">
                                    <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="email"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Email:
                                        <span className="text-gray-400"></span>
                                      </label>
                                      <div className="mt-1 sm:col-span-2 sm:mt-0">
                                        <input
                                          value={(email && email.entry) || ""}
                                          disabled={true}
                                          type="text"
                                          name="email"
                                          id="email"
                                          className="block w-72 max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm bg-gray-100"
                                        />
                                      </div>
                                    </div>
                                    <div className="space-y-6 sm:space-y-5">
                                      <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                        <label
                                          htmlFor="email"
                                          className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                        >
                                          ebookshelf access expires:
                                          <span className="text-gray-400"></span>
                                        </label>
                                        <div className="mt-1 sm:col-span-2 sm:mt-0">
                                          <Datepicker
                                            value={userExpiry}
                                            onChange={handleValueChangeDate}
                                            asSingle={true}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                    {/* <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="Name"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Name
                                      </label>
                                      <div className="mt-1 sm:col-span-2 sm:mt-0">
                                        <input
                                          value={(Name && Name.entry) || ""}
                                          onChange={updateSelection}
                                          type="text"
                                          name="Name"
                                          id="Name"
                                          className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                        />
                                      </div>
                                    </div> */}
                                  </div>
                                </div>
                              </div>

                              <div className="pt-5">
                                <div className="flex justify-end">
                                  <Link href="/">
                                    <div className="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                      Cancel
                                    </div>
                                  </Link>
                                  <button
                                    onClick={updateProfile}
                                    className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                  >
                                    Update
                                  </button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div>
                              <div className="">
                                <h2 className="text-md text-gray-900">
                                  User created
                                </h2>

                                <div className="flex pt-3">
                                  <div className="pr-3">
                                    <button
                                      type="button"
                                      onClick={resetAddAdminUser}
                                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-secondaryPurpleA focus:outline-none"
                                    >
                                      Add another user
                                    </button>
                                  </div>
                                  <div>
                                    <Link href="/">
                                      <div
                                        type="button"
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-secondaryPurpleA focus:outline-none"
                                      >
                                        Finished
                                      </div>
                                    </Link>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      </div>
                    </div>
                  </main>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default AddAdminUser;
