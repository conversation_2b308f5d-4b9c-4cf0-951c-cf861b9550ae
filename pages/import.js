import { useLayoutEffect, useState, useEffect, useRef } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";
import Link from "next/link";
import { useRouter } from "next/router";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const ImportUser = () => {
  const router = useRouter();
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);

  // new state

  const [newUsers, setNewUsers] = useState([]);

  const checkbox = useRef();
  const [checked, setChecked] = useState(false);

  const [indeterminate, setIndeterminate] = useState(false);
  const [selectedPeople, setSelectedPeople] = useState([]);

  // remove debug
  const [responseJson, setResponseJson] = useState({});

  //   const [mobileNumber, setMobileNumber] = useState([]);
  const [Name, setName] = useState([]);
  const [day, setDay] = useState([]);
  const [month, setMonth] = useState([]);
  const [year, setYear] = useState([]);
  const [gender, setGender] = useState(null);
  const [role, setRole] = useState(null);
  const [province, setProvince] = useState(null);
  const [district, setDistrict] = useState(null);
  const [healthFacility, setHealthFacility] = useState(null);
  const [email, setEmail] = useState([]);

  const [userCreated, setUserCreated] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState(false);

  const [errorMsg, setErrorMsg] = useState("");

  /* --- DEBUG --- */

  // console.log(session);

  // console.log(userData);

  // console.log(responseJson);

  //   console.log(email);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    getProfile();
  }, [session]);

  // update currSelection when entry changes
  const updateSelection = (e) => {
    const { value: value } = e.target;
    const type = e.target.id;

    // console.log(value);

    setNewUsers({
      entry: value,
      type: type,
    });
  };

  // process the user data
  const getNewUsers = () => {
    // process and output the user data to pass to createUsers function

    // console.log(newUsers);

    const userList = newUsers.entry.split("\n");

    userList.map(function (em, index) {
      const account = em.split(",");
      //   console.log(account);
      addUser(account); // pass in account and then destructure in function
    });
  };

  //   console.log(newUsers);

  async function getProfile() {
    try {
      setLoading(true);
      const user = supabase.auth.user();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .neq("id", user.id);
      // .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function addUser(account) {
    // const newDOB = year.entry + "-" + month.entry + "-" + day.entry;

    const email = {
      entry: account[1],
      type: "email",
    };

    // console.log(mobileNumber);

    const userDataToSend = {
      name: account[0],
      email: email,
    };

    try {
      setLoading(true);
      //   setResponseJson({ loading: true });
      const { data, error } = await supabase.functions.invoke("add-user", {
        body: JSON.stringify(email),
      });

      if (error) alert(error);

      if (data) {
        if (data.error) {
          console.log(data.error);
          setErrorOnSubmit(data.error);
        } else {
          setResponseJson(data);
          addUserProfile(data, userDataToSend);
          setErrorOnSubmit(false);
          setUserCreated(true);
        }
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  /// add user profile

  async function addUserProfile(dataSent, userData) {
    try {
      setLoading(true);

      //   const newDOB = year.entry + "-" + month.entry + "-" + day.entry;

      const dataEnter = {
        id: dataSent.user.user.id,
        name: userData.name,
        email: userData.email.entry,
      };

      const { data, error } = await supabase
        .from("profiles")
        .insert(dataEnter)
        .select();

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      setLoading(false);
      // console.log("record created");
    }
  }

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <div>
              <LoginForm />
            </div>
          ) : (
            <>
              {userData && userData.length == 0 ? (
                <NotAuthorised />
              ) : (
                <div className="min-h-full">
                  <Header />

                  <main>
                    <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                      <div className="sm:flex sm:items-center">
                        <div className="sm:flex-auto">
                          <h1 className="text-xl font-semibold text-gray-900 pt-1 pb-6">
                            Import users
                          </h1>
                          {/* Help text */}
                          {/* <p className="mt-2 text-sm text-gray-700">
                            Paste your CSV file below
                          </p> */}
                        </div>

                        {errorOnSubmit ? (
                          <div className="text-red-500">{errorOnSubmit}</div>
                        ) : null}
                      </div>

                      <div className="mt-0 bg-white rounded-b-lg shadow-sm mb-3 pb-6">
                        <>
                          {!userCreated ? (
                            <div className="bg-navyBlue flex flex-col  py-6 sm:px-6 lg:px-8">
                              <div className="sm:mx-auto sm:w-full sm:max-w-md">
                                <h2 className="font-Ubuntu mt-3 text-center text-2xl font-extrabold text-primary">
                                  Add users
                                </h2>
                              </div>

                              <div className="mt-3 sm:mx-auto w-full m-6">
                                <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                                  <div>
                                    <label
                                      htmlFor="email"
                                      className="block text-sm font-medium text-gray-700"
                                    >
                                      Paste your CSV file below. Please use the
                                      format:{" "}
                                      <b>
                                        name,email with no spaces after commas.
                                      </b>
                                      <br />
                                      One entry per line
                                    </label>
                                    <div className="mt-1">
                                      <textarea
                                        value={newUsers && newUsers.entry}
                                        onChange={updateSelection}
                                        rows="4"
                                        cols="50"
                                        id="newusers"
                                        name="newusers"
                                        required
                                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                      />
                                    </div>
                                  </div>

                                  <div className="mt-6">
                                    <button
                                      onClick={getNewUsers}
                                      //   type="submit"
                                      className="flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                      Add users
                                    </button>

                                    {/* {errorMessage && (
                                      <p className="error">{errorMessage}</p>
                                    )} */}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="bg-navyBlue flex flex-col  py-6 sm:px-6 lg:px-8">
                              <div className="sm:mx-auto sm:w-full sm:max-w-md">
                                <h2 className="font-Ubuntu mt-3 text-center text-2xl font-extrabold text-primary">
                                  Users imported and created
                                </h2>
                              </div>

                              <div className="mt-3 sm:mx-auto w-full m-6">
                                <div className="mt-6">
                                  <Link href="/">
                                    <button
                                      //   type="submit"
                                      className="flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                      View users
                                    </button>
                                  </Link>

                                  {/* {errorMessage && (
                                    <p className="error">{errorMessage}</p>
                                  )} */}
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      </div>
                    </div>
                  </main>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default ImportUser;
