import { useLayoutEffect, useState, useEffect, useRef, Fragment } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import AddUserMenu from "@components/ui/AddUserMenu";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";

import { Dialog, Transition } from "@headlessui/react";
import { ExclamationCircleIcon } from "@heroicons/react/outline";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const AdminUsers = () => {
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [userProfile, setUserProfile] = useState(null);

  const checkbox = useRef();
  const [checked, setChecked] = useState(false);

  const [indeterminate, setIndeterminate] = useState(false);
  const [selectedPeople, setSelectedPeople] = useState([]);

  const [open, setOpen] = useState(false);
  const [userDeleteId, setUserDeleteId] = useState(null);
  const [userDeleteName, setUserDeleteName] = useState(null);

  const cancelButtonRef = useRef(null);

  /* --- DEBUG --- */

  // console.log(userProfile);

  // console.log(userDeleteId);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);
  useEffect(() => {
    getProfiles();
  }, [session]);

  useEffect(() => {
    getUserProfile();
  }, [session]);

  // remove? / multiple select
  // useLayoutEffect(() => {
  //   const isIndeterminate =
  //     selectedPeople &&
  //     selectedPeople.length &&
  //     selectedPeople.length > 0 &&
  //     selectedPeople.length < people.length;
  //   setChecked(selectedPeople.length === people.length);
  //   setIndeterminate(isIndeterminate);
  //   checkbox.current.indeterminate = isIndeterminate;
  // }, [selectedPeople]);

  function toggleAll() {
    setSelectedPeople(checked || indeterminate ? [] : people);
    setChecked(!checked && !indeterminate);
    setIndeterminate(false);
  }

  async function getUserProfile() {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserProfile(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getProfiles() {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("admin_user", true)
        .neq("id", user.id);
      // .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  const deleteUser = (id, name) => {
    setOpen(true);
    setUserDeleteId(id);
    setUserDeleteName(name);
  };

  const deleteUserConfirm = (id) => {
    setOpen(false);
    deleteUserProfile(userDeleteId);
  };

  async function deleteUserProfile(id) {
    try {
      setLoading(true);

      let { error } = await supabase
        .from("profiles")
        .delete()
        .match({ id: id });

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      // await subscribeForDelete();
      //   setTaskIndex(1);
      deleteUserAdmin(id);
      setLoading(false);
    }
  }

  async function deleteUserAdmin(id) {
    const userDataToSend = {
      entry: id,
    };

    try {
      setLoading(true);

      let { error } = await supabase
        .from("admins")
        .delete()
        .match({ user_id: id });

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      try {
        setLoading(true);
        //   setResponseJson({ loading: true });
        const { data, error } = await supabase.functions.invoke("delete-user", {
          body: JSON.stringify(userDataToSend),
        });

        if (error) alert(error);

        if (data) {
          if (data.error) {
            console.log(data.error);
            // setErrorOnSubmit(data.error);
          } else {
            // console.log(data);
          }
        }
      } catch (error) {
        console.log(error);
      } finally {
        getProfiles(); // reload the data to remove from table
        setLoading(false);
      }

      // setLoading(false);
    }
  }

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <div>
              <LoginForm />
            </div>
          ) : (
            <>
              {/* {userData && userData.length == 0 ? ( */}
              {userProfile && userProfile.admin_user == false ? (
                <NotAuthorised />
              ) : (
                <div className="min-h-full">
                  <Header />

                  <Transition.Root show={open} as={Fragment}>
                    <Dialog
                      as="div"
                      className="relative z-10"
                      initialFocus={cancelButtonRef}
                      onClose={setOpen}
                    >
                      <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                      >
                        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                      </Transition.Child>

                      <div className="fixed inset-0 z-10 overflow-y-auto">
                        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                          <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                          >
                            <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                              <div className="sm:flex sm:items-start">
                                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                  <ExclamationCircleIcon
                                    className="h-6 w-6 text-red-600"
                                    aria-hidden="true"
                                  />
                                </div>
                                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                  <Dialog.Title
                                    as="h3"
                                    className="text-lg font-medium leading-6 text-gray-900"
                                  >
                                    Delete user account
                                  </Dialog.Title>
                                  <div className="mt-2">
                                    <p className="text-sm text-gray-500">
                                      Are you sure you want to delete the user{" "}
                                      {userDeleteName}?
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                                <button
                                  type="button"
                                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                                  onClick={() => deleteUserConfirm()}
                                >
                                  Delete
                                </button>
                                <button
                                  type="button"
                                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                                  onClick={() => setOpen(false)}
                                  ref={cancelButtonRef}
                                >
                                  Cancel
                                </button>
                              </div>
                            </Dialog.Panel>
                          </Transition.Child>
                        </div>
                      </div>
                    </Dialog>
                  </Transition.Root>

                  <main>
                    <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                      <div className="px-4 sm:px-6 lg:px-8">
                        <div className="sm:flex sm:items-center">
                          <div className="sm:flex-auto">
                            <h1 className="text-xl font-semibold text-gray-900 pt-1">
                              Manage admin users
                            </h1>
                            {/* Help text */}
                            {/* <p className="mt-2 text-sm text-gray-700">
                            Filter and select users, results are displayed in
                            the table below
                          </p> */}
                          </div>
                          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                            <AddUserMenu />

                            {/* <Link href="/adduser">
                            <div className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">
                              Add user
                            </div>
                          </Link> */}
                          </div>
                        </div>

                        {/* Filter */}

                        <div className="mt-8 flex flex-col">
                          <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                              <div className="relative overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                                {/* {selectedPeople.length > 0 && (
                                  <div className="absolute top-0 left-12 flex h-12 items-center space-x-3 bg-gray-50 sm:left-16">
                                    <button
                                      type="button"
                                      className="inline-flex items-center rounded border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-30"
                                    >
                                      Delete selected
                                    </button>
                                    <button
                                  type="button"
                                  className="inline-flex items-center rounded border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-30"
                                >
                                  Delete all
                                </button>
                                  </div>
                                )} */}
                                <table className="min-w-full table-fixed divide-y divide-gray-300">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th
                                        scope="col"
                                        className="relative w-12 px-6 sm:w-16 sm:px-8"
                                      >
                                        {/* <input
                                        type="checkbox"
                                        className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary sm:left-6"
                                        ref={checkbox}
                                        checked={checked}
                                        onChange={toggleAll}
                                      /> */}
                                      </th>
                                      <th
                                        scope="col"
                                        className="min-w-[12rem] py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"
                                      >
                                        Name
                                      </th>

                                      <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        email address
                                      </th>
                                      {/* <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        <span className="sr-only">Edit</span>
                                      </th> */}
                                      <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        <span className="sr-only">Delete</span>
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody className="divide-y divide-gray-200 bg-white">
                                    {userData &&
                                      userData.map((person) => (
                                        <tr
                                          key={person.id}
                                          className={
                                            selectedPeople.includes(person)
                                              ? "bg-gray-50"
                                              : undefined
                                          }
                                        >
                                          {userProfile &&
                                          userProfile.id !== person.id ? (
                                            <td className="relative w-12 px-6 sm:w-16 sm:px-8">
                                              {selectedPeople.includes(
                                                person
                                              ) && (
                                                <div className="absolute inset-y-0 left-0 w-0.5 bg-primary" />
                                              )}
                                              {/* <input
                                                type="checkbox"
                                                className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary sm:left-6"
                                                value={person.email || ""}
                                                checked={selectedPeople.includes(
                                                  person
                                                )}
                                                onChange={(e) =>
                                                  setSelectedPeople(
                                                    e.target.checked
                                                      ? [
                                                          ...selectedPeople,
                                                          person,
                                                        ]
                                                      : selectedPeople.filter(
                                                          (p) => p !== person
                                                        )
                                                  )
                                                }
                                              /> */}
                                            </td>
                                          ) : (
                                            <td className="relative w-12 px-6 sm:w-16 sm:px-8">
                                              {selectedPeople.includes(
                                                person
                                              ) && (
                                                <div className="absolute inset-y-0 left-0 w-0.5 bg-primary" />
                                              )}
                                              <input
                                                disabled={true}
                                                type="checkbox"
                                                className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary sm:left-6"
                                                value={person.email || ""}
                                                checked={selectedPeople.includes(
                                                  person
                                                )}
                                                onChange={(e) =>
                                                  setSelectedPeople(
                                                    e.target.checked
                                                      ? [
                                                          ...selectedPeople,
                                                          person,
                                                        ]
                                                      : selectedPeople.filter(
                                                          (p) => p !== person
                                                        )
                                                  )
                                                }
                                              />
                                            </td>
                                          )}
                                          <td
                                            className={classNames(
                                              "whitespace-nowrap py-4 pr-3 text-sm font-medium",
                                              selectedPeople.includes(person)
                                                ? "text-primary"
                                                : "text-gray-900"
                                            )}
                                          >
                                            {person.first_name}{" "}
                                            {person.last_name}
                                          </td>
                                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                            {person.email}
                                          </td>

                                          {/* <td className="whitespace-nowrap px-9 py-4 text-sm text-gray-500">
                                        <CheckCircleIcon className="h-5 w-5 text-primary" />
                                      </td> */}
                                          {/* <td className="whitespace-nowrap py-4 pr-4 text-right text-sm font-medium sm:pr-2">
                                            <a
                                              href="#"
                                              className="text-primary hover:text-indigo-900"
                                            >
                                              Edit
                                              <span className="sr-only">
                                                , {person.name}
                                              </span>
                                            </a>
                                          </td> */}
                                          {userProfile &&
                                          userProfile.id !== person.id ? (
                                            <td className="whitespace-nowrap py-4 text-right text-sm font-medium sm:pr-6">
                                              <button
                                                onClick={() =>
                                                  deleteUser(
                                                    person.id,
                                                    person.name
                                                  )
                                                }
                                                className="text-red-600 hover:text-indigo-900"
                                              >
                                                Delete
                                              </button>
                                            </td>
                                          ) : null}
                                        </tr>
                                      ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* <div className="px-4 py-4 sm:px-0">
                    <div className="h-96 rounded-lg border-4 border-dashed border-gray-200" />
                  </div> */}
                      {/* /End replace */}
                    </div>
                  </main>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default AdminUsers;
