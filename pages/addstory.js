import { useLayoutEffect, useState, useEffect, useRef } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";
import Link from "next/link";
import { useRouter } from "next/router";

const freeBooks = [
  {
    label: "Yes",
    value: "yes",
  },
  {
    label: "No",
    value: "no",
  },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const AddStory = () => {
  const router = useRouter();
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);

  const checkbox = useRef();
  const [checked, setChecked] = useState(false);

  const [indeterminate, setIndeterminate] = useState(false);
  const [selectedPeople, setSelectedPeople] = useState([]);

  // remove debug
  // const [responseJson, setResponseJson] = useState({});

  const [Name, setName] = useState([]);
  const [storyId, setStoryId] = useState([]);
  const [bookShopLink, setBookshopLink] = useState([]);
  const [freeBook, setFreeBook] = useState([]);
  const [thumbnail, setThumbnail] = useState([]);

  const [storyCreated, setStoryCreated] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState(false);

  // generate new legacy id
  const short = require("short-uuid");
  const translator = short();

  const slugify = require("slugify");

  /* --- DEBUG --- */

  // console.log(session);

  // console.log(userData);

  // console.log(responseJson);

  //   console.log(email);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    getProfile();
  }, [session]);

  // remove? / multiple select
  // useLayoutEffect(() => {
  //   const isIndeterminate =
  //     selectedPeople &&
  //     selectedPeople.length &&
  //     selectedPeople.length > 0 &&
  //     selectedPeople.length < people.length;
  //   setChecked(selectedPeople.length === people.length);
  //   setIndeterminate(isIndeterminate);
  //   checkbox.current.indeterminate = isIndeterminate;
  // }, [selectedPeople]);

  function toggleAll() {
    setSelectedPeople(checked || indeterminate ? [] : people);
    setChecked(!checked && !indeterminate);
    setIndeterminate(false);
  }

  async function getProfile() {
    try {
      setLoading(true);
      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .neq("id", user.id);
      // .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  /// add user profile

  async function addStory() {
    let slug = slugify(Name.entry);

    let legacyID = translator.new();

    try {
      setLoading(true);

      const dataEnter = {
        name: Name.entry,
        bookshop_link: bookShopLink.entry,
        free: freeBook.entry,
        slug: slug,
        thumbnail: thumbnail.entry,
        legacy_id: legacyID,
      };

      const { data, error } = await supabase
        .from("stories")
        .insert(dataEnter)
        .select();

      if (error) {
        throw error;
      }
    } catch (error) {
      console.log(error.message);
    } finally {
      setLoading(false);
      setStoryCreated(true);
      // console.log("record created");
    }
  }

  // update currSelection when entry changes
  const updateSelection = (e) => {
    const { value: value } = e.target;
    const type = e.target.id;

    // if (e.target.value !== "") {
    switch (type) {
      case "Name":
        setName({
          entry: value,
          type: type,
        });

        return;

      case "bookshopLink":
        setBookshopLink({
          entry: value,
          type: type,
        });

        return;

      case "freeBook":
        if (value == "yes") {
          setFreeBook({
            entry: true,
          });
        } else {
          setFreeBook({
            entry: false,
          });
        }

        return;

      case "thumbnail":
        setThumbnail({
          entry: value,
          type: type,
        });

        return;

      default:
        return;
    }
    // }
  };
  const resetStoryData = () => {
    setStoryCreated(false);
    setName([]);
    setStoryId([]);
    setFreeBook([]);
    setBookshopLink([]);
  };

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <div>
              <LoginForm />
            </div>
          ) : (
            <>
              {userData && userData.length == 0 ? (
                <NotAuthorised />
              ) : (
                <div className="min-h-full">
                  <Header />

                  <main>
                    <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                      <div className="sm:flex sm:items-center">
                        <div className="sm:flex-auto">
                          <h1 className="text-xl font-semibold text-gray-900 pt-1 pb-6">
                            Add story
                          </h1>
                          {/* Help text */}
                          {/* <p className="mt-2 text-sm text-gray-700">
                          Filter and select users, results are displayed in
                          the table below
                        </p> */}
                        </div>
                        {/* <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <button
                          onClick={invokeFunction}
                          // onClick={addUser}
                          type="button"
                          className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                          Add user
                        </button>
                      </div> */}
                        {errorOnSubmit ? (
                          <div className="text-red-500">{errorOnSubmit}</div>
                        ) : null}
                      </div>

                      <div className="mt-0 bg-white rounded-b-lg shadow-sm mb-3 pb-6">
                        <>
                          {!storyCreated ? (
                            <div className="space-y-8 divide-y divide-gray-200">
                              <div className="space-y-8 divide-y divide-gray-200 ">
                                <div className="space-y-6 pt-8 sm:space-y-5">
                                  <div>
                                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                                      Story details
                                    </h3>
                                    {/* <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                  Use a permanent address where you can
                                  receive mail.
                                </p> */}
                                  </div>
                                  <div className="space-y-6 sm:space-y-5">
                                    <div className="space-y-6 sm:space-y-5">
                                      <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                        <label
                                          htmlFor="Name"
                                          className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                        >
                                          Title
                                        </label>
                                        <div className="mt-1 sm:col-span-2 sm:mt-0">
                                          <input
                                            value={(Name && Name.entry) || ""}
                                            onChange={updateSelection}
                                            type="text"
                                            name="Name"
                                            id="Name"
                                            className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                          />
                                        </div>
                                      </div>
                                      <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                        <label
                                          htmlFor="Name"
                                          className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                        >
                                          Bookshop link
                                        </label>
                                        <div className="mt-1 sm:col-span-2 sm:mt-0">
                                          <input
                                            value={
                                              (bookShopLink &&
                                                bookShopLink.entry) ||
                                              ""
                                            }
                                            onChange={updateSelection}
                                            type="text"
                                            name="bookshopLink"
                                            id="bookshopLink"
                                            className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                          />
                                        </div>
                                      </div>
                                      <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                        <label
                                          htmlFor="freeBook"
                                          className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                        >
                                          Free?
                                        </label>
                                        <div className="mt-1 sm:col-span-2 sm:mt-0">
                                          <select
                                            // value={
                                            //   (freeBook && freeBook.entry) || ""
                                            // }

                                            value={
                                              freeBook && freeBook.entry == true
                                                ? "yes"
                                                : "no" || ""
                                            }
                                            //   defaultValue=""
                                            onChange={updateSelection}
                                            id="freeBook"
                                            name="freeBook"
                                            className="mt-1 block w-36 rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                          >
                                            {freeBooks.map((option) => (
                                              <option
                                                key={option.value}
                                                value={option.value}
                                              >
                                                {option.label}
                                              </option>
                                            ))}

                                            {/* <option value="yes">yes</option>
                                          <option value="no">no</option> */}
                                          </select>
                                        </div>

                                        <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                          <label
                                            htmlFor="Name"
                                            className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                          >
                                            Thumbnail
                                          </label>
                                          <div className="mt-1 sm:col-span-2 sm:mt-0">
                                            <img
                                              className="w-24 rounded-md border border-1"
                                              src={`data:image/jpeg;base64,${
                                                thumbnail && thumbnail.entry
                                              }`}
                                            />
                                          </div>
                                        </div>
                                        <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                          <label
                                            htmlFor="Name"
                                            className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                          >
                                            Image string
                                          </label>
                                          <div className="mt-1 sm:col-span-2 sm:mt-0">
                                            <textarea
                                              type="text"
                                              id="thumbnail"
                                              name="thumbnail"
                                              onChange={updateSelection}
                                              // onChange={(e) => setLifeMoto(e.target.value)}
                                              rows={5}
                                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                              placeholder="image string base64"
                                              defaultValue={
                                                (thumbnail &&
                                                  thumbnail.entry) ||
                                                ""
                                              }
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="pt-5">
                                <div className="flex justify-end">
                                  <Link href="/stories">
                                    <div className="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                      Cancel
                                    </div>
                                  </Link>
                                  <button
                                    onClick={addStory}
                                    className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                  >
                                    Save
                                  </button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div>
                              <div className="">
                                <h2 className="text-md text-gray-900">
                                  Story created
                                </h2>

                                <div className="flex pt-3">
                                  <div className="pr-3">
                                    <button
                                      type="button"
                                      onClick={resetStoryData}
                                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-secondaryPurpleA focus:outline-none"
                                    >
                                      Add another story
                                    </button>
                                  </div>
                                  <div>
                                    <Link href="/stories">
                                      <div
                                        type="button"
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-secondaryPurpleA focus:outline-none"
                                      >
                                        Finished
                                      </div>
                                    </Link>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      </div>
                    </div>
                  </main>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default AddStory;
