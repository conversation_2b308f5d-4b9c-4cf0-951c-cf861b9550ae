import { useLayoutEffect, useState, useEffect, useRef, Fragment } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";
import Link from "next/link";
import { useRouter } from "next/router";
import { Dialog, Transition } from "@headlessui/react";
import { ExclamationCircleIcon } from "@heroicons/react/outline";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const EditBook = () => {
  const router = useRouter();
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [thumbnail, setThumbnail] = useState([]);

  const [bookImagesIds, setBookImagesIds] = useState([]);
  const [bookImages, setBookImages] = useState([]);
  const [bookLegacyId, setBookLegacyId] = useState([]);
  const [bookScenario, setBookScenario] = useState(null);

  const checkbox = useRef();
  const [checked, setChecked] = useState(false);

  const [indeterminate, setIndeterminate] = useState(false);
  const [selectedPeople, setSelectedPeople] = useState([]);

  const [open, setOpen] = useState(false);
  const [imageDeleteId, setImageDeleteId] = useState(null);
  const [imageDeleteName, setImageDeleteName] = useState(null);
  const [imageDeleteLegacyId, setImageDeleteLegacyId] = useState(null);

  const [Name, setName] = useState([]);
  const [bookId, setBookId] = useState([]);
  const [bookShopLink, setBookshopLink] = useState([]);

  const [userCreated, setUserCreated] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState(false);

  const cancelButtonRef = useRef(null);

  const { query } = useRouter();

  /* --- DEBUG --- */

  // console.log("bookImagesIds");
  // console.log(bookImagesIds);

  // console.log("imageDeleteLegacyId");
  // console.log(imageDeleteLegacyId);

  // console.log("bookImages");
  // console.log(bookImages);

  // console.log(Name);
  // console.log(email);
  // console.log(userId);

  // console.log(query);

  // console.log(session);

  //   console.log(responseJson);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    if (query && query.id) {
      getBook(query && query.id);
    }
  }, [query]);

  async function updateBook() {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("books")
        .update({
          name: Name.entry,
          bookshop_link: bookShopLink.entry,
          thumbnail: thumbnail.entry,
        })
        .eq("id", bookId);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getBook(id) {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("books")
        .select("*")
        .eq("id", id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        if (data.images) {
          let testArray = JSON.parse(data.images);
          // console.log("testArray");
          // console.log(testArray);
          setBookImagesIds(testArray);
        }

        setBookId(data.id);
        setName({
          entry: data.name,
        });
        setBookshopLink({
          entry: data.bookshop_link,
        });

        setThumbnail({
          entry: data.thumbnail,
        });
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getBookImages(ids) {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("images")
        .select("*")
        .in("legacy_id", ids);
      // .order("index", { ascending: false });
      if (data) {
        setBookImages(data);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }

  // update currSelection when entry changes
  const updateSelection = (e) => {
    const { value: value } = e.target;
    const type = e.target.id;

    // if (e.target.value !== "") {
    switch (type) {
      case "Name":
        setName({
          entry: value,
          type: type,
        });

        return;

      case "bookshopLink":
        setBookshopLink({
          entry: value,
          type: type,
        });

        return;

      case "thumbnail":
        setThumbnail({
          entry: value,
          type: type,
        });

        return;

      default:
        return;
    }
    // }
  };

  // change the order of the story images
  const images =
    bookImages &&
    bookImages.sort(function (a, b) {
      var textA = parseInt(a.image_id);
      var textB = parseInt(b.image_id);
      return textA < textB ? -1 : textA > textB ? 1 : 0;
    });

  const resetAddAdminUser = () => {
    setUserCreated(false);
    setName([]);
    setEmail([]);
  };

  const showImages = () => {
    // console.log("storyImagesIds");
    if (bookImagesIds) {
      // console.log("bookImagesIds");
      // console.log(bookImagesIds);

      // extract the ids
      let extractedIDs = bookImagesIds.map((a) => a.ID);

      // get the images
      getBookImages(extractedIDs);
    }
  };

  const deleteImage = (id, name, legacyId) => {
    setOpen(true);
    setImageDeleteId(id);
    setImageDeleteName(name);
    setImageDeleteLegacyId(legacyId);
  };

  const deleteImageConfirm = () => {
    setOpen(false);
    deleteImageDB(imageDeleteId);
  };

  async function deleteImageDB(id) {
    try {
      setLoading(true);

      let { error } = await supabase.from("images").delete().match({ id: id });

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      // need to remove the image from the story array!
      deleteImageFromBook();

      setLoading(false);
    }
  }

  async function deleteImageFromBook() {
    // get the current array

    let newArray = bookImagesIds;

    const index = newArray.findIndex((item) => item.ID === imageDeleteLegacyId);

    newArray.splice(index, 1);

    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("books")
        .update({
          images: newArray,
        })
        .eq("id", bookId);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      showImages(); // reload the data to remove from table
      setLoading(false);
    }
  }

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <div>
              <LoginForm />
            </div>
          ) : (
            <>
              {userData && userData.length == 0 ? (
                <NotAuthorised />
              ) : (
                <div className="min-h-full">
                  <Header />
                  <Transition.Root show={open} as={Fragment}>
                    <Dialog
                      as="div"
                      className="relative z-10"
                      initialFocus={cancelButtonRef}
                      onClose={setOpen}
                    >
                      <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                      >
                        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                      </Transition.Child>

                      <div className="fixed inset-0 z-10 overflow-y-auto">
                        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                          <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                          >
                            <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                              <div className="sm:flex sm:items-start">
                                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                  <ExclamationCircleIcon
                                    className="h-6 w-6 text-red-600"
                                    aria-hidden="true"
                                  />
                                </div>
                                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                  <Dialog.Title
                                    as="h3"
                                    className="text-lg font-medium leading-6 text-gray-900"
                                  >
                                    Delete image
                                  </Dialog.Title>
                                  <div className="mt-2">
                                    <p className="text-sm text-gray-500">
                                      Are you sure you want to delete the image{" "}
                                      {imageDeleteName}?
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                                <button
                                  type="button"
                                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                                  onClick={() => deleteImageConfirm()}
                                >
                                  Delete
                                </button>
                                <button
                                  type="button"
                                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                                  onClick={() => setOpen(false)}
                                  ref={cancelButtonRef}
                                >
                                  Cancel
                                </button>
                              </div>
                            </Dialog.Panel>
                          </Transition.Child>
                        </div>
                      </div>
                    </Dialog>
                  </Transition.Root>
                  <main>
                    <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                      <div className="sm:flex sm:items-center">
                        <div className="sm:flex-auto">
                          <h1 className="text-xl font-semibold text-gray-900 pt-1 pb-6">
                            Edit e-book
                          </h1>
                          {/* Help text */}
                          {/* <p className="mt-2 text-sm text-gray-700">
                          Filter and select users, results are displayed in
                          the table below
                        </p> */}
                        </div>
                        {/* <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <button
                          onClick={invokeFunction}
                          // onClick={addUser}
                          type="button"
                          className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                          Add user
                        </button>
                      </div> */}
                        {errorOnSubmit ? (
                          <div className="text-red-500">{errorOnSubmit}</div>
                        ) : null}
                      </div>

                      <div className="mt-0 bg-white rounded-b-lg shadow-sm mb-3 pb-6">
                        <div className="space-y-8 divide-y divide-gray-200">
                          <div className="space-y-8 divide-y divide-gray-200 ">
                            <div className="space-y-6 pt-8 sm:space-y-5">
                              <div>
                                <h3 className="text-lg font-medium leading-6 text-gray-900">
                                  e-book details
                                </h3>
                                {/* <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                  Use a permanent address where you can
                                  receive mail.
                                </p> */}
                              </div>
                              <div className="space-y-6 sm:space-y-5">
                                <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                  <label
                                    htmlFor="Name"
                                    className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                  >
                                    Title
                                  </label>
                                  <div className="mt-1 sm:col-span-2 sm:mt-0">
                                    <input
                                      value={(Name && Name.entry) || ""}
                                      onChange={updateSelection}
                                      type="text"
                                      name="Name"
                                      id="Name"
                                      className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                    />
                                  </div>
                                </div>
                                <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                  <label
                                    htmlFor="Name"
                                    className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                  >
                                    Bookshop link
                                  </label>
                                  <div className="mt-1 sm:col-span-2 sm:mt-0">
                                    <input
                                      value={
                                        (bookShopLink && bookShopLink.entry) ||
                                        ""
                                      }
                                      onChange={updateSelection}
                                      type="text"
                                      name="bookshopLink"
                                      id="bookshopLink"
                                      className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                    />
                                  </div>
                                </div>

                                <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                  <label
                                    htmlFor="Name"
                                    className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                  >
                                    Thumbnail
                                  </label>
                                  <div className="mt-1 sm:col-span-2 sm:mt-0">
                                    <img
                                      className="w-24 rounded-md border border-1"
                                      src={`data:image/jpeg;base64,${
                                        thumbnail && thumbnail.entry
                                      }`}
                                    />
                                  </div>
                                </div>
                                <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                  <label
                                    htmlFor="Name"
                                    className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                  >
                                    Image string
                                  </label>
                                  <div className="mt-1 sm:col-span-2 sm:mt-0">
                                    <textarea
                                      type="text"
                                      id="thumbnail"
                                      name="thumbnail"
                                      onChange={updateSelection}
                                      // onChange={(e) => setLifeMoto(e.target.value)}
                                      rows={5}
                                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                      placeholder="image string base64"
                                      defaultValue={
                                        (thumbnail && thumbnail.entry) || ""
                                      }
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="pt-5">
                            <div className="flex justify-end">
                              <Link href="/e-books">
                                <div className="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                  Cancel
                                </div>
                              </Link>
                              <button
                                onClick={updateBook}
                                className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                              >
                                Update
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* 
                              images table */}

                        <div>
                          {/* <h3 className="mt-6 text-lg font-medium leading-6 text-gray-900">
                                  Story images
                                </h3> */}
                          {/* <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                  Use a permanent address where you can
                                  receive mail.
                                </p> */}
                          <div className="p-3 pl-0 pt-6">
                            <button
                              onClick={showImages}
                              className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                              show images
                            </button>
                            <Link
                              href={"/add-ebook-image?id=" + bookId}
                              className="text-primary hover:text-indigo-900"
                            >
                              <button
                                // onClick={showImages}
                                className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-green-500 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-700 focus:ring-offset-2"
                              >
                                Add image
                              </button>
                            </Link>
                          </div>
                        </div>

                        <div className="mt-8 flex flex-col">
                          <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-12">
                            <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                              <div className="relative overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                                <table className="min-w-full table-fixed divide-y divide-gray-300">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        Image
                                      </th>
                                      <th
                                        scope="col"
                                        // className="min-w-[12rem] py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"
                                        className="px-5 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        Info
                                      </th>

                                      <th
                                        scope="col"
                                        // className="min-w-[12rem] py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        Index
                                      </th>

                                      <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        <span className="sr-only">Edit</span>
                                      </th>
                                      <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                      >
                                        <span className="sr-only">Delete</span>
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody className="divide-y divide-gray-200 bg-white">
                                    {images &&
                                      images.map((story) => (
                                        <tr
                                          key={story.id}
                                          // className={
                                          //   selectedPeople.includes(person)
                                          //     ? "bg-gray-50"
                                          //     : undefined
                                          // }
                                        >
                                          <td className="whitespace-nowrap px-5 py-4 text-sm text-gray-500">
                                            <img
                                              className="w-24 rounded-md border border-1"
                                              src={`data:image/jpeg;base64,${story.image_string}`}
                                            />
                                          </td>
                                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                            {story.info}
                                          </td>
                                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                            {story.image_id}
                                          </td>

                                          <td className="whitespace-nowrap py-4 pr-4 text-right text-sm font-medium sm:pr-2">
                                            <Link
                                              href={
                                                "/edit-ebook-image?imgId=" +
                                                story.id +
                                                "&id=" +
                                                bookId
                                              }
                                              className="text-primary hover:text-indigo-900"
                                            >
                                              Edit
                                              <span className="sr-only">
                                                , {story.name}
                                              </span>
                                            </Link>
                                          </td>
                                          <td className="whitespace-nowrap py-4 text-right text-sm font-medium sm:pr-6">
                                            <button
                                              onClick={() =>
                                                deleteImage(
                                                  story.id,
                                                  story.name,
                                                  story.legacy_id
                                                )
                                              }
                                              className="text-red-600 hover:text-indigo-900"
                                            >
                                              Delete
                                            </button>
                                          </td>
                                        </tr>
                                      ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </main>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default EditBook;
