import { useLayoutEffect, useState, useEffect, useRef } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";
import Link from "next/link";
import { useRouter } from "next/router";
import axios from "axios";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const Publish = () => {
  const [session, setSession] = useState(null);
  const [data, setData] = useState(null);
  const [disableButtonPreview, setDisableButtonPreview] = useState(false);
  const [disableButtonBuild, setDisableButtonBuild] = useState(false);

  /* --- DEBUG --- */

  // console.log(session);

  // console.log(userData);

  // console.log(responseJson);

  //   console.log(email);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  //   const fetchData = async () => {
  //     const response = await fetch("https://reqbin.com/echo/get/json"); // Replace with your API endpoint
  //     const result = await response.json();
  //     setData(result);
  //   };

  async function sendPreview() {
    try {
      await axios
        .post(
          "https://api.vercel.com/v1/integrations/deploy/prj_EZXfuVRhKscrxRqWw3d3NLAMhlLl/VkAAaY0hIE",
          {
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "true",
            },
          }
        )
        .then((response) => {
          console.log(response);
          setDisableButtonPreview(true);
        })
        .catch((error) => {
          // Handle error.
          console.log("An error occurred:", error);
        });
    } catch (error) {
      console.error("An unexpected error happened:", error.message);
    }
  }

  //   useEffect(() => {
  //     getProfile();
  //   }, [session]);

  // send preview request

  // send publish request

  return (
    <>
      <div>
        {/* Render your data here */}
        {data && <pre>{JSON.stringify(data, null, 2)}</pre>}
      </div>

      <div className="pt-5">
        <div>
          <h3 className="text-md leading-6 font-medium text-gray-900 p-4 pb-1">
            You can generate a preview to see how your changes will look like
            before publishing to live.
          </h3>
          <p className="p-4 pb-6 pt-0">
            Preview is available at:{" "}
            <a
              className="font-semibold text-blue-600"
              href="https://uat.booksbeyondwords.co.uk"
              target="_blank"
              rel="noopener noreferrer"
            >
              uat.booksbeyondwords.co.uk
            </a>
          </p>
        </div>
        <div className="flex justify-start">
          <button
            onClick={sendPreview}
            disabled={disableButtonPreview}
            className={
              !disableButtonPreview
                ? "ml-3 inline-flex justify-center rounded-md border border-transparent bg-primary py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                : "ml-3 inline-flex justify-center rounded-md border border-transparent bg-gray-300 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            }
          >
            Generate preview
          </button>
        </div>
      </div>

      <div className="pt-5">
        <div>
          <h3 className="text-md leading-6 font-medium text-gray-900 p-4">
            Once you are happy with the preview, you can publish your changes to
            live.
          </h3>
        </div>
        <div className="flex justify-start">
          <button
            disabled={disableButtonBuild}
            // onClick={sendPreview}
            className={
              !disableButtonBuild
                ? "ml-3 inline-flex justify-center rounded-md border border-transparent bg-green-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                : "ml-3 inline-flex justify-center rounded-md border border-transparent bg-gray-300 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            }
          >
            {" "}
            Publish to live
          </button>
        </div>
      </div>
    </>
  );
};

export default Publish;
