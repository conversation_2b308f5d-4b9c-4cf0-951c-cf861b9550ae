import { useLayoutEffect, useState, useEffect, useRef, Fragment } from "react";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";
import NotAuthorised from "@components/ui/NotAuthorised";
import Header from "@components/ui/Header";
import { Oval } from "svg-loaders-react";
import { CheckCircleIcon } from "@heroicons/react/solid";
import Link from "next/link";
import { useRouter } from "next/router";
import { Dialog, Transition } from "@headlessui/react";
import { ExclamationCircleIcon } from "@heroicons/react/outline";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const freeBooks = [
  {
    label: "Yes",
    value: "yes",
  },
  {
    label: "No",
    value: "no",
  },
];

const EditStory = () => {
  const router = useRouter();
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);

  const checkbox = useRef();
  const [checked, setChecked] = useState(false);

  const [indeterminate, setIndeterminate] = useState(false);
  const [selectedPeople, setSelectedPeople] = useState([]);

  const [open, setOpen] = useState(false);
  const [imageDeleteId, setImageDeleteId] = useState(null);
  const [imageDeleteName, setImageDeleteName] = useState(null);
  const [imageDeleteLegacyId, setImageDeleteLegacyId] = useState(null);

  const [Name, setName] = useState([]);
  const [storyId, setStoryId] = useState([]);
  const [bookShopLink, setBookshopLink] = useState([]);
  const [freeBook, setFreeBook] = useState([]);
  const [thumbnail, setThumbnail] = useState([]);
  const [storyImagesIds, setStoryImagesIds] = useState([]);
  const [storyImages, setStoryImages] = useState([]);
  const [storyLegacyId, setStoryLegacyId] = useState([]);
  const [storyScenarios, setStoryScenarios] = useState([]);

  const [editScenario, setEditScenario] = useState(null);

  const [allCategories, setAllCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedCategoryIDs, setSelectedCategoryIDs] = useState(null);

  const [allSubCategories, setAllSubCategories] = useState([]);
  const [selectedSubCategory, setSelectedSubCategory] = useState(null);

  const [allScenarios, setAllScenarios] = useState([]);
  const [selectedScenarios, setSelectedScenarios] = useState([]);

  const [userCreated, setUserCreated] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState(false);

  const cancelButtonRef = useRef(null);

  const { query } = useRouter();

  /* --- DEBUG --- */

  // console.log("editScenario");
  // console.log(editScenario);

  // console.log("storyScenario");
  // console.log(storyScenario && storyScenario);

  // console.log("selectedScenario");
  // console.log(selectedScenario);

  // console.log("storyLegacyId");
  // console.log(storyLegacyId);

  // console.log("selectedScenarioStories");
  // console.log(selectedScenarioStories);

  // console.log("selectedCategory");
  // console.log(selectedCategory);

  // console.log("allSubCategories");
  // console.log(allSubCategories);

  // console.log("selectedSubCategory");
  // console.log(selectedSubCategory);

  // console.log("DELETE");
  // console.log(imageDeleteId);
  // console.log(imageDeleteName);

  // console.log("storyImagesIds");
  // console.log(storyImagesIds);

  // console.log("storyImages");
  // console.log(storyImages);

  // console.log("thumbnail");
  // console.log(thumbnail);

  // console.log(Name);
  // console.log(email);
  // console.log(userId);

  // console.log(query);

  // console.log(session);

  //   console.log(responseJson);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    if (query && query.id) {
      getStory(query && query.id);
    }
  }, [query]);

  useEffect(() => {
    getAllCategories();
  }, [session]);

  async function updateStory() {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("stories")
        .update({
          name: Name.entry,
          bookshop_link: bookShopLink.entry,
          free: freeBook.entry,
          thumbnail: thumbnail.entry,
          scenario: selectedScenarios,
        })
        .eq("id", storyId);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
      // Update all selected scenarios
      await updateMultipleScenarios();
      // get the story
      getStory(query && query.id);
    }
  }

  // Update multiple scenarios data
  async function updateMultipleScenarios() {
    try {
      setLoading(true);

      // Remove story from old scenarios that are no longer selected
      const currentScenarioIds = storyScenarios.map((s) => s.legacy_id);
      const scenariosToRemove = currentScenarioIds.filter(
        (id) => !selectedScenarios.includes(id)
      );

      for (const scenarioId of scenariosToRemove) {
        await removeStoryFromScenario(scenarioId);
      }

      // Add story to new scenarios
      for (const scenarioId of selectedScenarios) {
        if (!currentScenarioIds.includes(scenarioId)) {
          await addStoryToScenario(scenarioId);
        }
      }
    } catch (error) {
      console.log("Error updating multiple scenarios:", error);
    } finally {
      setLoading(false);
    }
  }

  async function addStoryToScenario(scenarioId) {
    try {
      // Get current scenario data
      let { data: scenarioData, error } = await supabase
        .from("scenarios")
        .select("stories")
        .eq("legacy_id", scenarioId)
        .single();

      if (error) throw error;

      let storyIDs = scenarioData.stories
        ? JSON.parse(scenarioData.stories)
        : [];

      // Add story if not already present
      if (!storyIDs.includes(storyLegacyId)) {
        storyIDs.push(storyLegacyId);

        let { error: updateError } = await supabase
          .from("scenarios")
          .update({ stories: storyIDs })
          .eq("legacy_id", scenarioId);

        if (updateError) throw updateError;
      }
    } catch (error) {
      console.log("Error adding story to scenario:", error);
    }
  }

  async function removeStoryFromScenario(scenarioId) {
    try {
      // Get current scenario data
      let { data: scenarioData, error } = await supabase
        .from("scenarios")
        .select("stories")
        .eq("legacy_id", scenarioId)
        .single();

      if (error) throw error;

      let storyIDs = scenarioData.stories
        ? JSON.parse(scenarioData.stories)
        : [];

      // Remove story if present
      const index = storyIDs.indexOf(storyLegacyId);
      if (index > -1) {
        storyIDs.splice(index, 1);

        let { error: updateError } = await supabase
          .from("scenarios")
          .update({ stories: storyIDs })
          .eq("legacy_id", scenarioId);

        if (updateError) throw updateError;
      }
    } catch (error) {
      console.log("Error removing story from scenario:", error);
    }
  }

  async function updateStoryRemoveAllScenarios() {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("stories")
        .update({
          scenario: [],
        })
        .eq("id", storyId);

      if (error && status !== 406) {
        throw error;
      }

      // Remove story from all current scenarios
      for (const scenario of storyScenarios) {
        await removeStoryFromScenario(scenario.legacy_id);
      }

      if (data) {
        // setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
      setEditScenario(true);
      setStoryScenarios([]);
      setSelectedScenarios([]);
    }
  }

  async function getExistingScenarios(scenarioIds) {
    console.log("getExistingScenarios", scenarioIds);
    try {
      setLoading(true);

      if (!scenarioIds || scenarioIds.length === 0) {
        setStoryScenarios([]);
        return;
      }

      let { data, error, status } = await supabase
        .from("scenarios")
        .select("*")
        .in("legacy_id", scenarioIds);

      if (error && status !== 406) {
        throw error;
      }
      if (data) {
        console.log("Existing scenarios data:", data);
        setStoryScenarios(data);
      }
    } catch (error) {
      console.log("Error getting existing scenarios:", error);
    } finally {
      setLoading(false);
    }
  }

  async function getStory(id) {
    console.log("getStory");
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("stories")
        .select("*")
        .eq("id", id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        if (data.images) {
          let testArray = JSON.parse(data.images);
          setStoryImagesIds(testArray);
        }

        console.log(data);
        setStoryId(data.id);
        setName({
          entry: data.name,
        });
        setBookshopLink({
          entry: data.bookshop_link,
        });
        setFreeBook({
          entry: data.free,
        });

        setThumbnail({
          entry: data.thumbnail,
        });

        setStoryLegacyId(data.legacy_id);

        if (data.scenario && data.scenario.length > 0) {
          console.log("data.scenario");
          console.log(data.scenario);

          // Handle both array and single value for backward compatibility
          const scenarioIds = Array.isArray(data.scenario)
            ? data.scenario
            : [data.scenario];
          setSelectedScenarios(scenarioIds);
          getExistingScenarios(scenarioIds);
          setEditScenario(false);
        } else {
          setSelectedScenarios([]);
          setStoryScenarios([]);
          setEditScenario(true);
        }

        // setStoryScenario({
        //   entry: data.scenario,
        // });
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getAllCategories() {
    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("categories")
        .select("*");

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // console.log(data);
        setAllCategories(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getSubCategories(array) {
    // pass in the list of legacy ids from the selected category

    let subIDs = JSON.parse(array);

    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("subcategories")
        .select("*")
        .in("legacy_id", subIDs);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // console.log(data);
        setAllSubCategories(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getScenarios(array) {
    // pass in the list of legacy ids from the selected category

    let subIDs = JSON.parse(array);

    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("scenarios")
        .select("*")
        .in("legacy_id", subIDs);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // console.log(data);
        setAllScenarios(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function getStoryImages(ids) {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("images")
        .select("*")
        .in("legacy_id", ids);
      // .textSearch("legacy_id", [ids]);
      // storyImages = data;

      // not showing the first and last element in the array

      if (data) {
        // console.log("data");
        // console.log(data);
        setStoryImages(data);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }

  // update currSelection when entry changes
  const updateSelection = (e) => {
    const { value: value } = e.target;
    const type = e.target.id;

    // if (e.target.value !== "") {
    switch (type) {
      case "Name":
        setName({
          entry: value,
          type: type,
        });

        return;

      case "bookshopLink":
        setBookshopLink({
          entry: value,
          type: type,
        });

        return;

      case "freeBook":
        if (value == "yes") {
          setFreeBook({
            entry: true,
          });
        } else {
          setFreeBook({
            entry: false,
          });
        }

        return;

      case "thumbnail":
        setThumbnail({
          entry: value,
          type: type,
        });

        return;

      case "allCategories":
        setSelectedCategory(value);
        getSubCategories(value);

        return;

      case "subcategories":
        setSelectedSubCategory(value);
        getScenarios(value);

        return;

      case "scenario":
        // Handle multiple scenario selection
        const currentScenarios = [...selectedScenarios];
        if (currentScenarios.includes(value)) {
          // Remove if already selected
          const updatedScenarios = currentScenarios.filter(
            (id) => id !== value
          );
          setSelectedScenarios(updatedScenarios);
        } else {
          // Add if not selected
          setSelectedScenarios([...currentScenarios, value]);
        }
        return;

      default:
        return;
    }
    // }
  };

  // change the order of the story images
  const images =
    storyImages &&
    storyImages.sort(function (a, b) {
      var textA = a.image_id;
      var textB = b.image_id;
      return textA < textB ? -1 : textA > textB ? 1 : 0;
    });

  // console.log("images");
  // console.log(images);

  const toggleEditScenario = () => {
    // Clear all scenarios and enter edit mode
    updateStoryRemoveAllScenarios();
  };

  const clearScenarioSelections = () => {
    setSelectedCategory(null);
    setSelectedSubCategory(null);
    setAllSubCategories([]);
    setAllScenarios([]);
    setSelectedScenarios([]);
  };

  const resetAddAdminUser = () => {
    setUserCreated(false);
    setName([]);
    setEmail([]);
  };

  const showImages = () => {
    // console.log("storyImagesIds");
    if (storyImagesIds) {
      getStoryImages(storyImagesIds);
    }
  };

  const deleteImage = (id, name, legacyId) => {
    setOpen(true);
    setImageDeleteId(id);
    setImageDeleteName(name);
    setImageDeleteLegacyId(legacyId);
  };

  const deleteImageConfirm = () => {
    setOpen(false);
    deleteImageDB(imageDeleteId);
  };

  async function deleteImageDB(id) {
    try {
      setLoading(true);

      let { error } = await supabase.from("images").delete().match({ id: id });

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      // need to remove the image from the story array!
      deleteImageFromStory();

      setLoading(false);
    }
  }

  async function deleteImageFromStory() {
    // get the current array

    // console.log("storyImagesIds");
    // console.log(storyImagesIds);

    // console.log("legacyId");
    // console.log(imageDeleteLegacyId);

    let newArray = storyImagesIds;

    // remove the legacy id from the array

    const index = newArray.indexOf(imageDeleteLegacyId);
    if (index > -1) {
      // only splice array when item is found
      newArray.splice(index, 1); // 2nd parameter means remove one item only
    }

    try {
      setLoading(true);

      let { data, error, status } = await supabase
        .from("stories")
        .update({
          images: newArray,
        })
        .eq("id", storyId);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // setUserData(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      showImages(); // reload the data to remove from table
      setLoading(false);
    }
  }

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <div>
              <LoginForm />
            </div>
          ) : (
            <>
              {userData && userData.length == 0 ? (
                <NotAuthorised />
              ) : (
                <div className="min-h-full">
                  <Header />
                  <Transition.Root show={open} as={Fragment}>
                    <Dialog
                      as="div"
                      className="relative z-10"
                      initialFocus={cancelButtonRef}
                      onClose={setOpen}
                    >
                      <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                      >
                        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                      </Transition.Child>

                      <div className="fixed inset-0 z-10 overflow-y-auto">
                        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                          <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                          >
                            <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                              <div className="sm:flex sm:items-start">
                                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                  <ExclamationCircleIcon
                                    className="h-6 w-6 text-red-600"
                                    aria-hidden="true"
                                  />
                                </div>
                                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                  <Dialog.Title
                                    as="h3"
                                    className="text-lg font-medium leading-6 text-gray-900"
                                  >
                                    Delete image
                                  </Dialog.Title>
                                  <div className="mt-2">
                                    <p className="text-sm text-gray-500">
                                      Are you sure you want to delete the image{" "}
                                      {imageDeleteName}?
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                                <button
                                  type="button"
                                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                                  onClick={() => deleteImageConfirm()}
                                >
                                  Delete
                                </button>
                                <button
                                  type="button"
                                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                                  onClick={() => setOpen(false)}
                                  ref={cancelButtonRef}
                                >
                                  Cancel
                                </button>
                              </div>
                            </Dialog.Panel>
                          </Transition.Child>
                        </div>
                      </div>
                    </Dialog>
                  </Transition.Root>
                  <main>
                    <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                      <div className="sm:flex sm:items-center">
                        <div className="sm:flex-auto">
                          <h1 className="text-xl font-semibold text-gray-900 pt-1 pb-6">
                            Edit story
                          </h1>
                          {/* Help text */}
                          {/* <p className="mt-2 text-sm text-gray-700">
                          Filter and select users, results are displayed in
                          the table below
                        </p> */}
                        </div>
                        {/* <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <button
                          onClick={invokeFunction}
                          // onClick={addUser}
                          type="button"
                          className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                          Add user
                        </button>
                      </div> */}
                        {errorOnSubmit ? (
                          <div className="text-red-500">{errorOnSubmit}</div>
                        ) : null}
                      </div>

                      <div className="mt-0 bg-white rounded-b-lg shadow-sm mb-3 pb-6">
                        <>
                          {!userCreated ? (
                            <div className="space-y-8 divide-y divide-gray-200">
                              <div className="space-y-8 divide-y divide-gray-200 ">
                                <div className="space-y-6 pt-8 sm:space-y-5">
                                  <div>
                                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                                      Story details
                                    </h3>
                                    {/* <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                  Use a permanent address where you can
                                  receive mail.
                                </p> */}
                                  </div>
                                  <div className="space-y-6 sm:space-y-5">
                                    <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="Name"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Title
                                      </label>
                                      <div className="mt-1 sm:col-span-2 sm:mt-0">
                                        <input
                                          value={(Name && Name.entry) || ""}
                                          onChange={updateSelection}
                                          type="text"
                                          name="Name"
                                          id="Name"
                                          className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                        />
                                      </div>
                                    </div>
                                    <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="Name"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Bookshop link
                                      </label>
                                      <div className="mt-1 sm:col-span-2 sm:mt-0">
                                        <input
                                          value={
                                            (bookShopLink &&
                                              bookShopLink.entry) ||
                                            ""
                                          }
                                          onChange={updateSelection}
                                          type="text"
                                          name="bookshopLink"
                                          id="bookshopLink"
                                          className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:max-w-xs sm:text-sm"
                                        />
                                      </div>
                                    </div>
                                    <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="Name"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Scenario
                                      </label>
                                      {!editScenario ? (
                                        <div>
                                          <div className="mb-2">
                                            {storyScenarios.length > 0 ? (
                                              <div className="flex flex-wrap gap-2">
                                                {storyScenarios.map(
                                                  (scenario) => (
                                                    <span
                                                      key={scenario.legacy_id}
                                                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                                    >
                                                      {scenario.name}
                                                    </span>
                                                  )
                                                )}
                                              </div>
                                            ) : (
                                              <span className="text-gray-500">
                                                No scenarios assigned
                                              </span>
                                            )}
                                          </div>
                                          <button
                                            onClick={toggleEditScenario}
                                            className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                          >
                                            Edit scenarios
                                          </button>
                                        </div>
                                      ) : (
                                        <div className="mt-1 sm:col-span-2 sm:mt-0">
                                          <select
                                            value={selectedCategory || " "}
                                            //   defaultValue=""
                                            onChange={updateSelection}
                                            id="allCategories"
                                            name="allCategories"
                                            className="mt-1 block w-36 rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                          >
                                            <option>select category</option>
                                            {allCategories &&
                                              allCategories.map((option) => (
                                                <option
                                                  key={option.name}
                                                  value={option.subcategories}
                                                >
                                                  {option.name}
                                                </option>
                                              ))}
                                          </select>
                                          <select
                                            value={selectedSubCategory || " "}
                                            //   defaultValue=""
                                            onChange={updateSelection}
                                            id="subcategories"
                                            name="subcategories"
                                            className="mt-1 block w-36 rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                          >
                                            <option>select subcategory</option>
                                            {allSubCategories &&
                                              allSubCategories.map((option) => (
                                                <option
                                                  key={option.name}
                                                  value={option.scenarios}
                                                >
                                                  {option.name}
                                                </option>
                                              ))}
                                          </select>

                                          <div className="space-y-2">
                                            <label className="text-sm font-medium text-gray-700">
                                              Select scenarios (click to
                                              toggle):
                                            </label>
                                            <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                                              {allScenarios &&
                                              allScenarios.length > 0 ? (
                                                allScenarios.map((option) => (
                                                  <div
                                                    key={option.legacy_id}
                                                    className={`p-2 cursor-pointer rounded-md hover:bg-gray-100 ${
                                                      selectedScenarios.includes(
                                                        option.legacy_id
                                                      )
                                                        ? "bg-blue-100 border border-blue-300"
                                                        : "border border-transparent"
                                                    }`}
                                                    onClick={() =>
                                                      updateSelection({
                                                        target: {
                                                          id: "scenario",
                                                          value:
                                                            option.legacy_id,
                                                        },
                                                      })
                                                    }
                                                  >
                                                    <div className="flex items-center">
                                                      <input
                                                        type="checkbox"
                                                        checked={selectedScenarios.includes(
                                                          option.legacy_id
                                                        )}
                                                        onChange={() => {}}
                                                        className="mr-2"
                                                      />
                                                      <span className="text-sm">
                                                        {option.name}
                                                      </span>
                                                    </div>
                                                  </div>
                                                ))
                                              ) : (
                                                <div className="text-gray-500 text-sm">
                                                  No scenarios available. Please
                                                  select a subcategory first.
                                                </div>
                                              )}
                                            </div>
                                            {selectedScenarios.length > 0 && (
                                              <div className="mt-2">
                                                <span className="text-sm text-gray-600">
                                                  Selected:{" "}
                                                  {selectedScenarios.length}{" "}
                                                  scenario(s)
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                    <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                      <label
                                        htmlFor="freeBook"
                                        className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                      >
                                        Free?
                                      </label>
                                      <div className="mt-1 sm:col-span-2 sm:mt-0">
                                        <select
                                          // value={
                                          //   (freeBook && freeBook.entry) || ""
                                          // }

                                          value={
                                            freeBook && freeBook.entry == true
                                              ? "yes"
                                              : "no" || ""
                                          }
                                          //   defaultValue=""
                                          onChange={updateSelection}
                                          id="freeBook"
                                          name="freeBook"
                                          className="mt-1 block w-36 rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                        >
                                          {freeBooks.map((option) => (
                                            <option
                                              key={option.value}
                                              value={option.value}
                                            >
                                              {option.label}
                                            </option>
                                          ))}

                                          {/* <option value="yes">yes</option>
                                          <option value="no">no</option> */}
                                        </select>
                                      </div>

                                      <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                        <label
                                          htmlFor="Name"
                                          className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                        >
                                          Thumbnail
                                        </label>
                                        <div className="mt-1 sm:col-span-2 sm:mt-0">
                                          <img
                                            className="w-24 rounded-md border border-1"
                                            src={`data:image/jpeg;base64,${
                                              thumbnail && thumbnail.entry
                                            }`}
                                          />
                                        </div>
                                      </div>
                                      <div className="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
                                        <label
                                          htmlFor="Name"
                                          className="block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2"
                                        >
                                          Image string
                                        </label>
                                        <div className="mt-1 sm:col-span-2 sm:mt-0">
                                          <textarea
                                            type="text"
                                            id="thumbnail"
                                            name="thumbnail"
                                            onChange={updateSelection}
                                            // onChange={(e) => setLifeMoto(e.target.value)}
                                            rows={5}
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            placeholder="image string base64"
                                            defaultValue={
                                              (thumbnail && thumbnail.entry) ||
                                              ""
                                            }
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="pt-5">
                                <div className="flex justify-end">
                                  <Link href="/stories">
                                    <div className="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                      Cancel
                                    </div>
                                  </Link>
                                  <button
                                    onClick={updateStory}
                                    className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                  >
                                    Update
                                  </button>
                                </div>
                              </div>

                              {/* 
                              images table */}

                              <div>
                                {/* <h3 className="mt-6 text-lg font-medium leading-6 text-gray-900">
                                  Story images
                                </h3> */}
                                {/* <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                  Use a permanent address where you can
                                  receive mail.
                                </p> */}
                                <div className="p-3 pl-0 pt-6">
                                  <button
                                    onClick={showImages}
                                    className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                  >
                                    show images
                                  </button>
                                  <Link
                                    href={"/addimage?id=" + storyId}
                                    className="text-primary hover:text-indigo-900"
                                  >
                                    <button
                                      // onClick={showImages}
                                      className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-green-500 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-700 focus:ring-offset-2"
                                    >
                                      Add image
                                    </button>
                                  </Link>
                                </div>
                              </div>

                              <div className="mt-8 flex flex-col">
                                <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-12">
                                  <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                                    <div className="relative overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                                      <table className="min-w-full table-fixed divide-y divide-gray-300">
                                        <thead className="bg-gray-50">
                                          <tr>
                                            <th
                                              scope="col"
                                              className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                            >
                                              Image
                                            </th>
                                            <th
                                              scope="col"
                                              // className="min-w-[12rem] py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"
                                              className="px-5 py-3.5 text-left text-sm font-semibold text-gray-900"
                                            >
                                              Info
                                            </th>

                                            <th
                                              scope="col"
                                              // className="min-w-[12rem] py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"
                                              className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                            >
                                              Index
                                            </th>

                                            <th
                                              scope="col"
                                              className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                            >
                                              <span className="sr-only">
                                                Edit
                                              </span>
                                            </th>
                                            <th
                                              scope="col"
                                              className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                            >
                                              <span className="sr-only">
                                                Delete
                                              </span>
                                            </th>
                                          </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-200 bg-white">
                                          {images &&
                                            images.map((story) => (
                                              <tr
                                                key={story.id}
                                                // className={
                                                //   selectedPeople.includes(person)
                                                //     ? "bg-gray-50"
                                                //     : undefined
                                                // }
                                              >
                                                <td className="whitespace-nowrap px-5 py-4 text-sm text-gray-500">
                                                  <img
                                                    className="w-24 rounded-md border border-1"
                                                    src={`data:image/jpeg;base64,${story.image_string}`}
                                                  />
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                  {story.info}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                  {story.image_id}
                                                </td>

                                                <td className="whitespace-nowrap py-4 pr-4 text-right text-sm font-medium sm:pr-2">
                                                  <Link
                                                    href={
                                                      "/editimage?imgId=" +
                                                      story.id +
                                                      "&id=" +
                                                      storyId
                                                    }
                                                    className="text-primary hover:text-indigo-900"
                                                  >
                                                    Edit
                                                    <span className="sr-only">
                                                      , {story.name}
                                                    </span>
                                                  </Link>
                                                </td>
                                                <td className="whitespace-nowrap py-4 text-right text-sm font-medium sm:pr-6">
                                                  <button
                                                    onClick={() =>
                                                      deleteImage(
                                                        story.id,
                                                        story.name,
                                                        story.legacy_id
                                                      )
                                                    }
                                                    className="text-red-600 hover:text-indigo-900"
                                                  >
                                                    Delete
                                                  </button>
                                                </td>
                                              </tr>
                                            ))}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div>
                              <div className="">
                                <h2 className="text-md text-gray-900">
                                  User created
                                </h2>

                                <div className="flex pt-3">
                                  <div className="pr-3">
                                    <button
                                      type="button"
                                      onClick={resetAddAdminUser}
                                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-secondaryPurpleA focus:outline-none"
                                    >
                                      Add another user
                                    </button>
                                  </div>
                                  <div>
                                    <Link href="/">
                                      <div
                                        type="button"
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-secondaryPurpleA focus:outline-none"
                                      >
                                        Finished
                                      </div>
                                    </Link>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      </div>
                    </div>
                  </main>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default EditStory;
