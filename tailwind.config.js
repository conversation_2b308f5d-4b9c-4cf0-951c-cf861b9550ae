/** @type {import('tailwindcss').Config} */

const colors = require("tailwindcss/colors");

module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./node_modules/react-tailwindcss-datepicker/dist/index.esm.js",
  ],
  theme: {
    // fontFamily: {
    //   Montserrat: ["Montserrat", "sans-serif"],
    // },
    fontFamily: {
      sans: ["Montserrat", "sans-serif"],
      serif: ["Montserrat", "sans-serif"],
      mono: ["Montserrat", "sans-serif"],
      heading: ["Montserrat", "sans-serif"],
      body: ["Montserrat", "sans-serif"],
    },
    extend: {
      colors: {
        primary: "#2D519F",
        secondary: "#43A6DD",
        disabled: "#92949c",
        white: "#ffffff",
        error: "#FF0000",
        greyDark: "#383a3e",
        greyMedium: "#808289",
        greylight: "#9d9fa6",
      },

      textColor: {
        primary: "#2D519F",
      },
    },
  },
  plugins: [
    // ...
    require("@tailwindcss/forms"),
  ],
};
