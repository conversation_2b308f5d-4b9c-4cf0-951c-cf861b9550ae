import React from "react";

export default function FormFilter() {
  return (
    <div className="mt-8 flex flex-col">
      <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8"></div>
      <div className="grid grid-cols-4 gap-4">
        <div className="">
          <select
            id="country"
            name="country"
            autoComplete="country-name"
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          >
            <option>Country</option>
            <option>Kenya</option>
            <option>Malawi</option>
          </select>
        </div>
        <div>
          <select
            id="country"
            name="country"
            autoComplete="country-name"
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          >
            <option>Gender</option>
            <option>Male</option>
            <option>Female</option>
          </select>
        </div>
        <div>
          <select
            id="country"
            name="country"
            autoComplete="country-name"
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          >
            <option>Completed</option>
            <option>true</option>
            <option>false</option>
          </select>
        </div>
        <div>
          <select
            id="country"
            name="country"
            autoComplete="country-name"
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm"
          >
            <option>Profession</option>
            <option>CHW</option>
          </select>
        </div>
      </div>
    </div>
  );
}
