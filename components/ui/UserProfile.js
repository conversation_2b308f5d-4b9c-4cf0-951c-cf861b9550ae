import { Fragment, useState, useEffect } from "react";
import { supabase } from "@utils/supabaseClient";
import Avatar from "@components/Avatar";
import Link from "next/link";
import { Oval } from "svg-loaders-react";
import Agenda from "@components/ui/Agenda";
import Header from "@components/ui/Header";
import Lottie from "react-lottie";
import animationData from "@animation/profile";

const defaultOptions = {
  loop: false,
  autoplay: true,
  animationData: animationData,
  rendererSettings: {
    preserveAspectRatio: "xMidYMid slice",
  },
};

export default function UserProfile({ session }) {
  const [loading, setLoading] = useState(true);
  const [username, setUsername] = useState(null);
  const [website, setWebsite] = useState(null);
  const [avatar_url, setAvatarUrl] = useState(null);
  const [firstName, setFirstName] = useState(null);
  const [secondName, setSecondName] = useState(null);
  const [lifeMoto, setLifeMoto] = useState(null);
  const [country, setCountry] = useState(null);
  const [profileStatus, setProfileStatus] = useState(null);
  const [debriefStatus, setDebriefStatus] = useState(null);
  const [initialProfileStatus, setInitialProfileStatus] = useState(false);
  const [taskIndex, setTaskIndex] = useState(null);

  useEffect(() => {
    getProfile();
  }, [session]);

  /* --- DEBUG --- */

  // console.log(session);
  // console.log("avatar_url");
  // console.log(avatar_url);
  // console.log(taskIndex);

  /* --- DEBUG --- */

  async function getProfile() {
    try {
      setLoading(true);
      const user = supabase.auth.user();

      let { data, error, status } = await supabase
        .from("profiles")
        .select(
          `username, website, avatar_url, firstname, secondname, profile_status, debrief_status, lifemoto, country, task_index`
        )
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUsername(data.username);
        setWebsite(data.website);
        setAvatarUrl(data.avatar_url);
        setFirstName(data.firstname);
        setSecondName(data.secondname);
        setProfileStatus(data.profile_status);
        setDebriefStatus(data.debrief_status);
        setLifeMoto(data.lifemoto);
        setCountry(data.country);
        setTaskIndex(data.task_index);
        // setEmailAddress(data.profile_status);
      }
    } catch (error) {
      alert(error.message);
    } finally {
      setLoading(false);
    }
  }

  async function updateProfileInitial() {
    try {
      setLoading(true);
      const user = supabase.auth.user();

      const updates = {
        id: user.id,
        firstname: firstName,
        secondname: secondName,
        lifemoto: lifeMoto,
        country: country,
        profile_status: "complete",
        debrief_status: "current",
        updated_at: new Date(),
      };

      let { error } = await supabase.from("profiles").upsert(updates, {
        returning: "minimal", // Don't return the value after inserting
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      setLoading(false);
      setInitialProfileStatus(true);
    }
  }

  async function updateProfile({ username, website, avatar_url }) {
    try {
      setLoading(true);
      const user = supabase.auth.user();

      const updates = {
        id: user.id,
        firstname: firstName,
        secondname: secondName,
        lifemoto: lifeMoto,
        country: country,
        avatar_url,
        updated_at: new Date(),
      };

      let { error } = await supabase.from("profiles").upsert(updates, {
        returning: "minimal", // Don't return the value after inserting
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      setLoading(false);
    }
  }

  function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
  }

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          <div className="min-h-full">
            {/* When the mobile menu is open, add `overflow-hidden` to the `body` element to prevent double scrollbars */}
            <Header avatar_url={avatar_url} userType="participant" />
            <div className="py-4">
              <div className="max-w-3xl mx-auto sm:px-6 lg:max-w-7xl lg:px-8 lg:grid lg:grid-cols-12 lg:gap-4">
                <div className="hidden lg:block lg:col-span-3 xl:col-span-3 bg-white rounded-lg shadow-sm p-3 pt-4">
                  {/*  Agenda nav  */}

                  <Agenda
                    // debriefStatus={debriefStatus}
                    // profileStatus={profileStatus}
                    taskIndex={taskIndex}
                  />
                </div>

                {/* Main Task */}
                <main className="lg:col-span-9 xl:col-span-9">
                  <div className="px-4 sm:px-0"></div>

                  {profileStatus !== "current" ? (
                    <div className="space-y-6 sm:px-6 lg:col-span-9 lg:px-0">
                      <div className="shadow sm:overflow-hidden sm:rounded-lg">
                        <div className="space-y-6 bg-white py-6 px-4 sm:p-6">
                          <div>
                            <h3 className="text-lg font-medium leading-6 text-gray-900">
                              Profile
                            </h3>
                          </div>

                          <div className="grid grid-cols-6 gap-6">
                            <div className="col-span-6 sm:col-span-3">
                              <label
                                htmlFor="first-name"
                                className="block text-sm font-medium text-gray-700"
                              >
                                First name
                              </label>
                              <input
                                defaultValue={firstName || ""}
                                id="firstname"
                                type="text"
                                // value={firstName || ""}
                                onChange={(e) => setFirstName(e.target.value)}
                                className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                              />
                            </div>

                            <div className="col-span-6 sm:col-span-3">
                              <label
                                htmlFor="last-name"
                                className="block text-sm font-medium text-gray-700"
                              >
                                Last name
                              </label>
                              <input
                                defaultValue={secondName || ""}
                                id="secondname"
                                type="text"
                                // value={secondName || ""}
                                onChange={(e) => setSecondName(e.target.value)}
                                className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                              />
                            </div>

                            {/* <div className="col-span-6 sm:col-span-4">
                                <label
                                  htmlFor="email-address"
                                  className="block text-sm font-medium text-gray-700"
                                >
                                  Email address
                                </label>
                                <input
                                  type="text"
                                  name="email-address"
                                  id="email-address"
                                  autoComplete="email"
                                  className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                />
                              </div> */}

                            <div className="col-span-6 sm:col-span-3">
                              <label
                                htmlFor="country"
                                className="block text-sm font-medium text-gray-700"
                              >
                                Country
                              </label>
                              <select
                                defaultValue={country || ""}
                                // value={country || ""}
                                onChange={(e) => setCountry(e.target.value)}
                                id="country"
                                name="country"
                                className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                              >
                                <option>select country</option>
                                <option value="Afganistan">Afghanistan</option>
                                <option value="Albania">Albania</option>
                                <option value="Algeria">Algeria</option>
                                <option value="American Samoa">
                                  American Samoa
                                </option>
                                <option value="Andorra">Andorra</option>
                                <option value="Angola">Angola</option>
                                <option value="Anguilla">Anguilla</option>
                                <option value="Antigua &amp; Barbuda">
                                  Antigua &amp; Barbuda
                                </option>
                                <option value="Argentina">Argentina</option>
                                <option value="Armenia">Armenia</option>
                                <option value="Aruba">Aruba</option>
                                <option value="Australia">Australia</option>
                                <option value="Austria">Austria</option>
                                <option value="Azerbaijan">Azerbaijan</option>
                                <option value="Bahamas">Bahamas</option>
                                <option value="Bahrain">Bahrain</option>
                                <option value="Bangladesh">Bangladesh</option>
                                <option value="Barbados">Barbados</option>
                                <option value="Belarus">Belarus</option>
                                <option value="Belgium">Belgium</option>
                                <option value="Belize">Belize</option>
                                <option value="Benin">Benin</option>
                                <option value="Bermuda">Bermuda</option>
                                <option value="Bhutan">Bhutan</option>
                                <option value="Bolivia">Bolivia</option>
                                <option value="Bonaire">Bonaire</option>
                                <option value="Bosnia &amp; Herzegovina">
                                  Bosnia &amp; Herzegovina
                                </option>
                                <option value="Botswana">Botswana</option>
                                <option value="Brazil">Brazil</option>
                                <option value="British Indian Ocean Ter">
                                  British Indian Ocean Ter
                                </option>
                                <option value="Brunei">Brunei</option>
                                <option value="Bulgaria">Bulgaria</option>
                                <option value="Burkina Faso">
                                  Burkina Faso
                                </option>
                                <option value="Burundi">Burundi</option>
                                <option value="Cambodia">Cambodia</option>
                                <option value="Cameroon">Cameroon</option>
                                <option value="Canada">Canada</option>
                                <option value="Canary Islands">
                                  Canary Islands
                                </option>
                                <option value="Cape Verde">Cape Verde</option>
                                <option value="Cayman Islands">
                                  Cayman Islands
                                </option>
                                <option value="Central African Republic">
                                  Central African Republic
                                </option>
                                <option value="Chad">Chad</option>
                                <option value="Channel Islands">
                                  Channel Islands
                                </option>
                                <option value="Chile">Chile</option>
                                <option value="China">China</option>
                                <option value="Christmas Island">
                                  Christmas Island
                                </option>
                                <option value="Cocos Island">
                                  Cocos Island
                                </option>
                                <option value="Colombia">Colombia</option>
                                <option value="Comoros">Comoros</option>
                                <option value="Congo">Congo</option>
                                <option value="Cook Islands">
                                  Cook Islands
                                </option>
                                <option value="Costa Rica">Costa Rica</option>
                                <option value="Cote DIvoire">
                                  Cote D&apos;Ivoire
                                </option>
                                <option value="Croatia">Croatia</option>
                                <option value="Cuba">Cuba</option>
                                <option value="Curaco">Curacao</option>
                                <option value="Cyprus">Cyprus</option>
                                <option value="Czech Republic">
                                  Czech Republic
                                </option>
                                <option value="Denmark">Denmark</option>
                                <option value="Djibouti">Djibouti</option>
                                <option value="Dominica">Dominica</option>
                                <option value="Dominican Republic">
                                  Dominican Republic
                                </option>
                                <option value="East Timor">East Timor</option>
                                <option value="Ecuador">Ecuador</option>
                                <option value="Egypt">Egypt</option>
                                <option value="El Salvador">El Salvador</option>
                                <option value="Equatorial Guinea">
                                  Equatorial Guinea
                                </option>
                                <option value="Eritrea">Eritrea</option>
                                <option value="Estonia">Estonia</option>
                                <option value="Ethiopia">Ethiopia</option>
                                <option value="Falkland Islands">
                                  Falkland Islands
                                </option>
                                <option value="Faroe Islands">
                                  Faroe Islands
                                </option>
                                <option value="Fiji">Fiji</option>
                                <option value="Finland">Finland</option>
                                <option value="France">France</option>
                                <option value="French Guiana">
                                  French Guiana
                                </option>
                                <option value="French Polynesia">
                                  French Polynesia
                                </option>
                                <option value="French Southern Ter">
                                  French Southern Ter
                                </option>
                                <option value="Gabon">Gabon</option>
                                <option value="Gambia">Gambia</option>
                                <option value="Georgia">Georgia</option>
                                <option value="Germany">Germany</option>
                                <option value="Ghana">Ghana</option>
                                <option value="Gibraltar">Gibraltar</option>
                                <option value="Great Britain">
                                  Great Britain
                                </option>
                                <option value="Greece">Greece</option>
                                <option value="Greenland">Greenland</option>
                                <option value="Grenada">Grenada</option>
                                <option value="Guadeloupe">Guadeloupe</option>
                                <option value="Guam">Guam</option>
                                <option value="Guatemala">Guatemala</option>
                                <option value="Guinea">Guinea</option>
                                <option value="Guyana">Guyana</option>
                                <option value="Haiti">Haiti</option>
                                <option value="Hawaii">Hawaii</option>
                                <option value="Honduras">Honduras</option>
                                <option value="Hong Kong">Hong Kong</option>
                                <option value="Hungary">Hungary</option>
                                <option value="Iceland">Iceland</option>
                                <option value="India">India</option>
                                <option value="Indonesia">Indonesia</option>
                                <option value="Iran">Iran</option>
                                <option value="Iraq">Iraq</option>
                                <option value="Ireland">Ireland</option>
                                <option value="Isle of Man">Isle of Man</option>
                                <option value="Israel">Israel</option>
                                <option value="Italy">Italy</option>
                                <option value="Jamaica">Jamaica</option>
                                <option value="Japan">Japan</option>
                                <option value="Jordan">Jordan</option>
                                <option value="Kazakhstan">Kazakhstan</option>
                                <option value="Kenya">Kenya</option>
                                <option value="Kiribati">Kiribati</option>
                                <option value="Korea North">Korea North</option>
                                <option value="Korea Sout">Korea South</option>
                                <option value="Kuwait">Kuwait</option>
                                <option value="Kyrgyzstan">Kyrgyzstan</option>
                                <option value="Laos">Laos</option>
                                <option value="Latvia">Latvia</option>
                                <option value="Lebanon">Lebanon</option>
                                <option value="Lesotho">Lesotho</option>
                                <option value="Liberia">Liberia</option>
                                <option value="Libya">Libya</option>
                                <option value="Liechtenstein">
                                  Liechtenstein
                                </option>
                                <option value="Lithuania">Lithuania</option>
                                <option value="Luxembourg">Luxembourg</option>
                                <option value="Macau">Macau</option>
                                <option value="Macedonia">Macedonia</option>
                                <option value="Madagascar">Madagascar</option>
                                <option value="Malaysia">Malaysia</option>
                                <option value="Malawi">Malawi</option>
                                <option value="Maldives">Maldives</option>
                                <option value="Mali">Mali</option>
                                <option value="Malta">Malta</option>
                                <option value="Marshall Islands">
                                  Marshall Islands
                                </option>
                                <option value="Martinique">Martinique</option>
                                <option value="Mauritania">Mauritania</option>
                                <option value="Mauritius">Mauritius</option>
                                <option value="Mayotte">Mayotte</option>
                                <option value="Mexico">Mexico</option>
                                <option value="Midway Islands">
                                  Midway Islands
                                </option>
                                <option value="Moldova">Moldova</option>
                                <option value="Monaco">Monaco</option>
                                <option value="Mongolia">Mongolia</option>
                                <option value="Montserrat">Montserrat</option>
                                <option value="Morocco">Morocco</option>
                                <option value="Mozambique">Mozambique</option>
                                <option value="Myanmar">Myanmar</option>
                                <option value="Nambia">Nambia</option>
                                <option value="Nauru">Nauru</option>
                                <option value="Nepal">Nepal</option>
                                <option value="Netherland Antilles">
                                  Netherland Antilles
                                </option>
                                <option value="Netherlands">
                                  Netherlands (Holland, Europe)
                                </option>
                                <option value="Nevis">Nevis</option>
                                <option value="New Caledonia">
                                  New Caledonia
                                </option>
                                <option value="New Zealand">New Zealand</option>
                                <option value="Nicaragua">Nicaragua</option>
                                <option value="Niger">Niger</option>
                                <option value="Nigeria">Nigeria</option>
                                <option value="Niue">Niue</option>
                                <option value="Norfolk Island">
                                  Norfolk Island
                                </option>
                                <option value="Norway">Norway</option>
                                <option value="Oman">Oman</option>
                                <option value="Pakistan">Pakistan</option>
                                <option value="Palau Island">
                                  Palau Island
                                </option>
                                <option value="Palestine">Palestine</option>
                                <option value="Panama">Panama</option>
                                <option value="Papua New Guinea">
                                  Papua New Guinea
                                </option>
                                <option value="Paraguay">Paraguay</option>
                                <option value="Peru">Peru</option>
                                <option value="Phillipines">Philippines</option>
                                <option value="Pitcairn Island">
                                  Pitcairn Island
                                </option>
                                <option value="Poland">Poland</option>
                                <option value="Portugal">Portugal</option>
                                <option value="Puerto Rico">Puerto Rico</option>
                                <option value="Qatar">Qatar</option>
                                <option value="Republic of Montenegro">
                                  Republic of Montenegro
                                </option>
                                <option value="Republic of Serbia">
                                  Republic of Serbia
                                </option>
                                <option value="Reunion">Reunion</option>
                                <option value="Romania">Romania</option>
                                <option value="Russia">Russia</option>
                                <option value="Rwanda">Rwanda</option>
                                <option value="St Barthelemy">
                                  St Barthelemy
                                </option>
                                <option value="St Eustatius">
                                  St Eustatius
                                </option>
                                <option value="St Helena">St Helena</option>
                                <option value="St Kitts-Nevis">
                                  St Kitts-Nevis
                                </option>
                                <option value="St Lucia">St Lucia</option>
                                <option value="St Maarten">St Maarten</option>
                                <option value="St Pierre &amp; Miquelon">
                                  St Pierre &amp; Miquelon
                                </option>
                                <option value="St Vincent &amp; Grenadines">
                                  St Vincent &amp; Grenadines
                                </option>
                                <option value="Saipan">Saipan</option>
                                <option value="Samoa">Samoa</option>
                                <option value="Samoa American">
                                  Samoa American
                                </option>
                                <option value="San Marino">San Marino</option>
                                <option value="Sao Tome &amp; Principe">
                                  Sao Tome &amp; Principe
                                </option>
                                <option value="Saudi Arabia">
                                  Saudi Arabia
                                </option>
                                <option value="Senegal">Senegal</option>
                                <option value="Serbia">Serbia</option>
                                <option value="Seychelles">Seychelles</option>
                                <option value="Sierra Leone">
                                  Sierra Leone
                                </option>
                                <option value="Singapore">Singapore</option>
                                <option value="Slovakia">Slovakia</option>
                                <option value="Slovenia">Slovenia</option>
                                <option value="Solomon Islands">
                                  Solomon Islands
                                </option>
                                <option value="Somalia">Somalia</option>
                                <option value="South Africa">
                                  South Africa
                                </option>
                                <option value="Spain">Spain</option>
                                <option value="Sri Lanka">Sri Lanka</option>
                                <option value="Sudan">Sudan</option>
                                <option value="Suriname">Suriname</option>
                                <option value="Swaziland">Swaziland</option>
                                <option value="Sweden">Sweden</option>
                                <option value="Switzerland">Switzerland</option>
                                <option value="Syria">Syria</option>
                                <option value="Tahiti">Tahiti</option>
                                <option value="Taiwan">Taiwan</option>
                                <option value="Tajikistan">Tajikistan</option>
                                <option value="Tanzania">Tanzania</option>
                                <option value="Thailand">Thailand</option>
                                <option value="Togo">Togo</option>
                                <option value="Tokelau">Tokelau</option>
                                <option value="Tonga">Tonga</option>
                                <option value="Trinidad &amp; Tobago">
                                  Trinidad &amp; Tobago
                                </option>
                                <option value="Tunisia">Tunisia</option>
                                <option value="Turkey">Turkey</option>
                                <option value="Turkmenistan">
                                  Turkmenistan
                                </option>
                                <option value="Turks &amp; Caicos Is">
                                  Turks &amp; Caicos Is
                                </option>
                                <option value="Tuvalu">Tuvalu</option>
                                <option value="Uganda">Uganda</option>
                                <option value="Ukraine">Ukraine</option>
                                <option value="United Arab Erimates">
                                  United Arab Emirates
                                </option>
                                <option value="United Kingdom">
                                  United Kingdom
                                </option>
                                <option value="United States of America">
                                  United States of America
                                </option>
                                <option value="Uraguay">Uruguay</option>
                                <option value="Uzbekistan">Uzbekistan</option>
                                <option value="Vanuatu">Vanuatu</option>
                                <option value="Vatican City State">
                                  Vatican City State
                                </option>
                                <option value="Venezuela">Venezuela</option>
                                <option value="Vietnam">Vietnam</option>
                                <option value="Virgin Islands (Brit)">
                                  Virgin Islands (Brit)
                                </option>
                                <option value="Virgin Islands (USA)">
                                  Virgin Islands (USA)
                                </option>
                                <option value="Wake Island">Wake Island</option>
                                <option value="Wallis &amp; Futana Is">
                                  Wallis &amp; Futana Is
                                </option>
                                <option value="Yemen">Yemen</option>
                                <option value="Zaire">Zaire</option>
                                <option value="Zambia">Zambia</option>
                                <option value="Zimbabwe">Zimbabwe</option>
                              </select>
                            </div>
                          </div>

                          <div className="col-span-3">
                            <label
                              htmlFor="about"
                              className="block text-sm font-medium text-gray-700"
                            >
                              Life moto
                            </label>
                            <div className="mt-1">
                              <textarea
                                type="text"
                                id="lifemoto"
                                name="lifemoto"
                                // value={lifeMoto || ""}
                                onChange={(e) => setLifeMoto(e.target.value)}
                                rows={3}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                placeholder="Type your life moto"
                                defaultValue={lifeMoto || ""}
                              />
                            </div>
                          </div>

                          <div className="col-span-3">
                            <label className="block text-sm font-medium text-gray-700">
                              Photo
                            </label>
                            <div className="mt-1 flex items-center">
                              <Avatar
                                edit={true}
                                url={avatar_url}
                                onUpload={(url) => {
                                  setAvatarUrl(url);
                                  updateProfile({
                                    username,
                                    website,
                                    avatar_url: url,
                                  });
                                }}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="bg-white px-4 py-3 text-right sm:px-6">
                          <button
                            className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            onClick={() =>
                              updateProfile({
                                username,
                                website,
                                avatar_url,
                              })
                            }
                            disabled={loading}
                          >
                            Update
                          </button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="mt-0 bg-white rounded-lg shadow-sm mb-3">
                      {/* QUESTIONS */}
                      <>
                        {!initialProfileStatus ? (
                          <div className="space-y-6 sm:px-6 lg:col-span-9 lg:px-0">
                            <div className="shadow sm:overflow-hidden sm:rounded-lg">
                              <div className="space-y-6 bg-white py-6 px-4 sm:p-6">
                                <div>
                                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                                    Add your life moto and choose a photo for
                                    your avatar.
                                  </h3>
                                </div>

                                <div className="grid grid-cols-6 gap-6">
                                  {/* <div className="col-span-6 sm:col-span-3">
                                    <label
                                      htmlFor="first-name"
                                      className="block text-sm font-medium text-gray-700"
                                    >
                                      First name
                                    </label>
                                    <input
                                      defaultValue={firstName || ""}
                                      id="firstname"
                                      type="text"
                                      // value={firstName || ""}
                                      onChange={(e) =>
                                        setFirstName(e.target.value)
                                      }
                                      className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                    />
                                  </div> */}

                                  {/* <div className="col-span-6 sm:col-span-3">
                                    <label
                                      htmlFor="last-name"
                                      className="block text-sm font-medium text-gray-700"
                                    >
                                      Last name
                                    </label>
                                    <input
                                      defaultValue={secondName || ""}
                                      id="secondname"
                                      type="text"
                                      // value={secondName || ""}
                                      onChange={(e) =>
                                        setSecondName(e.target.value)
                                      }
                                      className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                    />
                                  </div> */}

                                  {/* <div className="col-span-6 sm:col-span-4">
                                <label
                                  htmlFor="email-address"
                                  className="block text-sm font-medium text-gray-700"
                                >
                                  Email address
                                </label>
                                <input
                                  type="text"
                                  name="email-address"
                                  id="email-address"
                                  autoComplete="email"
                                  className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                />
                              </div> */}

                                  {/* <div className="col-span-6 sm:col-span-3">
                                    <label
                                      htmlFor="country"
                                      className="block text-sm font-medium text-gray-700"
                                    >
                                      Country
                                    </label>
                                    <select
                                      defaultValue={country || ""}
                                      // value={country || ""}
                                      onChange={(e) =>
                                        setCountry(e.target.value)
                                      }
                                      id="country"
                                      name="country"
                                      className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                                    >
                                      <option>select country</option>
                                      <option value="AF">Afghanistan</option>
                                      <option value="AX">Aland Islands</option>
                                      <option value="AL">Albania</option>
                                      <option value="DZ">Algeria</option>
                                      <option value="AS">American Samoa</option>
                                      <option value="AD">Andorra</option>
                                      <option value="AO">Angola</option>
                                      <option value="AI">Anguilla</option>
                                      <option value="AQ">Antarctica</option>
                                      <option value="AG">
                                        Antigua and Barbuda
                                      </option>
                                      <option value="AR">Argentina</option>
                                      <option value="AM">Armenia</option>
                                      <option value="AW">Aruba</option>
                                      <option value="AU">Australia</option>
                                      <option value="AT">Austria</option>
                                      <option value="AZ">Azerbaijan</option>
                                      <option value="BS">Bahamas</option>
                                      <option value="BH">Bahrain</option>
                                      <option value="BD">Bangladesh</option>
                                      <option value="BB">Barbados</option>
                                      <option value="BY">Belarus</option>
                                      <option value="BE">Belgium</option>
                                      <option value="BZ">Belize</option>
                                      <option value="BJ">Benin</option>
                                      <option value="BM">Bermuda</option>
                                      <option value="BT">Bhutan</option>
                                      <option value="BO">Bolivia</option>
                                      <option value="BQ">
                                        Bonaire, Sint Eustatius and Saba
                                      </option>
                                      <option value="BA">
                                        Bosnia and Herzegovina
                                      </option>
                                      <option value="BW">Botswana</option>
                                      <option value="BV">Bouvet Island</option>
                                      <option value="BR">Brazil</option>
                                      <option value="IO">
                                        British Indian Ocean Territory
                                      </option>
                                      <option value="BN">
                                        Brunei Darussalam
                                      </option>
                                      <option value="BG">Bulgaria</option>
                                      <option value="BF">Burkina Faso</option>
                                      <option value="BI">Burundi</option>
                                      <option value="KH">Cambodia</option>
                                      <option value="CM">Cameroon</option>
                                      <option value="CA">Canada</option>
                                      <option value="CV">Cape Verde</option>
                                      <option value="KY">Cayman Islands</option>
                                      <option value="CF">
                                        Central African Republic
                                      </option>
                                      <option value="TD">Chad</option>
                                      <option value="CL">Chile</option>
                                      <option value="CN">China</option>
                                      <option value="CX">
                                        Christmas Island
                                      </option>
                                      <option value="CC">
                                        Cocos (Keeling) Islands
                                      </option>
                                      <option value="CO">Colombia</option>
                                      <option value="KM">Comoros</option>
                                      <option value="CG">Congo</option>
                                      <option value="CD">
                                        Congo, Democratic Republic of the Congo
                                      </option>
                                      <option value="CK">Cook Islands</option>
                                      <option value="CR">Costa Rica</option>
                                      <option value="CI">Cote D'Ivoire</option>
                                      <option value="HR">Croatia</option>
                                      <option value="CU">Cuba</option>
                                      <option value="CW">Curacao</option>
                                      <option value="CY">Cyprus</option>
                                      <option value="CZ">Czech Republic</option>
                                      <option value="DK">Denmark</option>
                                      <option value="DJ">Djibouti</option>
                                      <option value="DM">Dominica</option>
                                      <option value="DO">
                                        Dominican Republic
                                      </option>
                                      <option value="EC">Ecuador</option>
                                      <option value="EG">Egypt</option>
                                      <option value="SV">El Salvador</option>
                                      <option value="GQ">
                                        Equatorial Guinea
                                      </option>
                                      <option value="ER">Eritrea</option>
                                      <option value="EE">Estonia</option>
                                      <option value="ET">Ethiopia</option>
                                      <option value="FK">
                                        Falkland Islands (Malvinas)
                                      </option>
                                      <option value="FO">Faroe Islands</option>
                                      <option value="FJ">Fiji</option>
                                      <option value="FI">Finland</option>
                                      <option value="FR">France</option>
                                      <option value="GF">French Guiana</option>
                                      <option value="PF">
                                        French Polynesia
                                      </option>
                                      <option value="TF">
                                        French Southern Territories
                                      </option>
                                      <option value="GA">Gabon</option>
                                      <option value="GM">Gambia</option>
                                      <option value="GE">Georgia</option>
                                      <option value="DE">Germany</option>
                                      <option value="GH">Ghana</option>
                                      <option value="GI">Gibraltar</option>
                                      <option value="GR">Greece</option>
                                      <option value="GL">Greenland</option>
                                      <option value="GD">Grenada</option>
                                      <option value="GP">Guadeloupe</option>
                                      <option value="GU">Guam</option>
                                      <option value="GT">Guatemala</option>
                                      <option value="GG">Guernsey</option>
                                      <option value="GN">Guinea</option>
                                      <option value="GW">Guinea-Bissau</option>
                                      <option value="GY">Guyana</option>
                                      <option value="HT">Haiti</option>
                                      <option value="HM">
                                        Heard Island and Mcdonald Islands
                                      </option>
                                      <option value="VA">
                                        Holy See (Vatican City State)
                                      </option>
                                      <option value="HN">Honduras</option>
                                      <option value="HK">Hong Kong</option>
                                      <option value="HU">Hungary</option>
                                      <option value="IS">Iceland</option>
                                      <option value="IN">India</option>
                                      <option value="ID">Indonesia</option>
                                      <option value="IR">
                                        Iran, Islamic Republic of
                                      </option>
                                      <option value="IQ">Iraq</option>
                                      <option value="IE">Ireland</option>
                                      <option value="IM">Isle of Man</option>
                                      <option value="IL">Israel</option>
                                      <option value="IT">Italy</option>
                                      <option value="JM">Jamaica</option>
                                      <option value="JP">Japan</option>
                                      <option value="JE">Jersey</option>
                                      <option value="JO">Jordan</option>
                                      <option value="KZ">Kazakhstan</option>
                                      <option value="KE">Kenya</option>
                                      <option value="KI">Kiribati</option>
                                      <option value="KP">
                                        Korea, Democratic People's Republic of
                                      </option>
                                      <option value="KR">
                                        Korea, Republic of
                                      </option>
                                      <option value="XK">Kosovo</option>
                                      <option value="KW">Kuwait</option>
                                      <option value="KG">Kyrgyzstan</option>
                                      <option value="LA">
                                        Lao People's Democratic Republic
                                      </option>
                                      <option value="LV">Latvia</option>
                                      <option value="LB">Lebanon</option>
                                      <option value="LS">Lesotho</option>
                                      <option value="LR">Liberia</option>
                                      <option value="LY">
                                        Libyan Arab Jamahiriya
                                      </option>
                                      <option value="LI">Liechtenstein</option>
                                      <option value="LT">Lithuania</option>
                                      <option value="LU">Luxembourg</option>
                                      <option value="MO">Macao</option>
                                      <option value="MK">
                                        Macedonia, the Former Yugoslav Republic
                                        of
                                      </option>
                                      <option value="MG">Madagascar</option>
                                      <option value="MW">Malawi</option>
                                      <option value="MY">Malaysia</option>
                                      <option value="MV">Maldives</option>
                                      <option value="ML">Mali</option>
                                      <option value="MT">Malta</option>
                                      <option value="MH">
                                        Marshall Islands
                                      </option>
                                      <option value="MQ">Martinique</option>
                                      <option value="MR">Mauritania</option>
                                      <option value="MU">Mauritius</option>
                                      <option value="YT">Mayotte</option>
                                      <option value="MX">Mexico</option>
                                      <option value="FM">
                                        Micronesia, Federated States of
                                      </option>
                                      <option value="MD">
                                        Moldova, Republic of
                                      </option>
                                      <option value="MC">Monaco</option>
                                      <option value="MN">Mongolia</option>
                                      <option value="ME">Montenegro</option>
                                      <option value="MS">Montserrat</option>
                                      <option value="MA">Morocco</option>
                                      <option value="MZ">Mozambique</option>
                                      <option value="MM">Myanmar</option>
                                      <option value="NA">Namibia</option>
                                      <option value="NR">Nauru</option>
                                      <option value="NP">Nepal</option>
                                      <option value="NL">Netherlands</option>
                                      <option value="AN">
                                        Netherlands Antilles
                                      </option>
                                      <option value="NC">New Caledonia</option>
                                      <option value="NZ">New Zealand</option>
                                      <option value="NI">Nicaragua</option>
                                      <option value="NE">Niger</option>
                                      <option value="NG">Nigeria</option>
                                      <option value="NU">Niue</option>
                                      <option value="NF">Norfolk Island</option>
                                      <option value="MP">
                                        Northern Mariana Islands
                                      </option>
                                      <option value="NO">Norway</option>
                                      <option value="OM">Oman</option>
                                      <option value="PK">Pakistan</option>
                                      <option value="PW">Palau</option>
                                      <option value="PS">
                                        Palestinian Territory, Occupied
                                      </option>
                                      <option value="PA">Panama</option>
                                      <option value="PG">
                                        Papua New Guinea
                                      </option>
                                      <option value="PY">Paraguay</option>
                                      <option value="PE">Peru</option>
                                      <option value="PH">Philippines</option>
                                      <option value="PN">Pitcairn</option>
                                      <option value="PL">Poland</option>
                                      <option value="PT">Portugal</option>
                                      <option value="PR">Puerto Rico</option>
                                      <option value="QA">Qatar</option>
                                      <option value="RE">Reunion</option>
                                      <option value="RO">Romania</option>
                                      <option value="RU">
                                        Russian Federation
                                      </option>
                                      <option value="RW">Rwanda</option>
                                      <option value="BL">
                                        Saint Barthelemy
                                      </option>
                                      <option value="SH">Saint Helena</option>
                                      <option value="KN">
                                        Saint Kitts and Nevis
                                      </option>
                                      <option value="LC">Saint Lucia</option>
                                      <option value="MF">Saint Martin</option>
                                      <option value="PM">
                                        Saint Pierre and Miquelon
                                      </option>
                                      <option value="VC">
                                        Saint Vincent and the Grenadines
                                      </option>
                                      <option value="WS">Samoa</option>
                                      <option value="SM">San Marino</option>
                                      <option value="ST">
                                        Sao Tome and Principe
                                      </option>
                                      <option value="SA">Saudi Arabia</option>
                                      <option value="SN">Senegal</option>
                                      <option value="RS">Serbia</option>
                                      <option value="CS">
                                        Serbia and Montenegro
                                      </option>
                                      <option value="SC">Seychelles</option>
                                      <option value="SL">Sierra Leone</option>
                                      <option value="SG">Singapore</option>
                                      <option value="SX">Sint Maarten</option>
                                      <option value="SK">Slovakia</option>
                                      <option value="SI">Slovenia</option>
                                      <option value="SB">
                                        Solomon Islands
                                      </option>
                                      <option value="SO">Somalia</option>
                                      <option value="ZA">South Africa</option>
                                      <option value="GS">
                                        South Georgia and the South Sandwich
                                        Islands
                                      </option>
                                      <option value="SS">South Sudan</option>
                                      <option value="ES">Spain</option>
                                      <option value="LK">Sri Lanka</option>
                                      <option value="SD">Sudan</option>
                                      <option value="SR">Suriname</option>
                                      <option value="SJ">
                                        Svalbard and Jan Mayen
                                      </option>
                                      <option value="SZ">Swaziland</option>
                                      <option value="SE">Sweden</option>
                                      <option value="CH">Switzerland</option>
                                      <option value="SY">
                                        Syrian Arab Republic
                                      </option>
                                      <option value="TW">
                                        Taiwan, Province of China
                                      </option>
                                      <option value="TJ">Tajikistan</option>
                                      <option value="TZ">
                                        Tanzania, United Republic of
                                      </option>
                                      <option value="TH">Thailand</option>
                                      <option value="TL">Timor-Leste</option>
                                      <option value="TG">Togo</option>
                                      <option value="TK">Tokelau</option>
                                      <option value="TO">Tonga</option>
                                      <option value="TT">
                                        Trinidad and Tobago
                                      </option>
                                      <option value="TN">Tunisia</option>
                                      <option value="TR">Turkey</option>
                                      <option value="TM">Turkmenistan</option>
                                      <option value="TC">
                                        Turks and Caicos Islands
                                      </option>
                                      <option value="TV">Tuvalu</option>
                                      <option value="UG">Uganda</option>
                                      <option value="UA">Ukraine</option>
                                      <option value="AE">
                                        United Arab Emirates
                                      </option>
                                      <option value="GB">United Kingdom</option>
                                      <option value="US">United States</option>
                                      <option value="UM">
                                        United States Minor Outlying Islands
                                      </option>
                                      <option value="UY">Uruguay</option>
                                      <option value="UZ">Uzbekistan</option>
                                      <option value="VU">Vanuatu</option>
                                      <option value="VE">Venezuela</option>
                                      <option value="VN">Viet Nam</option>
                                      <option value="VG">
                                        Virgin Islands, British
                                      </option>
                                      <option value="VI">
                                        Virgin Islands, U.s.
                                      </option>
                                      <option value="WF">
                                        Wallis and Futuna
                                      </option>
                                      <option value="EH">Western Sahara</option>
                                      <option value="YE">Yemen</option>
                                      <option value="ZM">Zambia</option>
                                      <option value="ZW">Zimbabwe</option>
                                    </select>
                                  </div> */}
                                </div>

                                <div className="col-span-3">
                                  <label
                                    htmlFor="about"
                                    className="block text-sm font-medium text-gray-700"
                                  >
                                    Life moto
                                  </label>
                                  <div className="mt-1">
                                    <textarea
                                      type="text"
                                      id="lifemoto"
                                      name="lifemoto"
                                      // value={lifeMoto || ""}
                                      onChange={(e) =>
                                        setLifeMoto(e.target.value)
                                      }
                                      rows={3}
                                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                      placeholder="Type your life moto"
                                      defaultValue={lifeMoto || ""}
                                    />
                                  </div>
                                </div>

                                <div className="col-span-3">
                                  <label className="block text-sm font-medium text-gray-700">
                                    Photo
                                  </label>
                                  <div className="mt-1 flex items-center">
                                    <Avatar
                                      edit={true}
                                      url={avatar_url}
                                      onUpload={(url) => {
                                        setAvatarUrl(url);
                                        updateProfile({
                                          username,
                                          website,
                                          avatar_url: url,
                                        });
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                              <div className="bg-white px-4 py-3 text-right sm:px-6">
                                <button
                                  className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                  // onClick={() =>
                                  //   updateProfile({
                                  //     username,
                                  //     website,
                                  //     avatar_url,
                                  //   })
                                  // }
                                  onClick={() => updateProfileInitial()}
                                  disabled={loading}
                                >
                                  Next
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div>
                            <div className="p-6">
                              <h2 className="text-lg font-semibold text-gray-900">
                                Great! You are now ready to start your immersion
                                debrief.
                              </h2>
                              <div className="p-6">
                                <Lottie
                                  options={defaultOptions}
                                  height={200}
                                  width={200}
                                />
                              </div>
                              <div className="pt-3">
                                <p>
                                  <Link href="/immersion-debrief">
                                    <a className="text-primaryDarkBlue">
                                      <span className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        Start debrief{" "}
                                      </span>
                                    </a>
                                  </Link>{" "}
                                  or{" "}
                                  <Link href="/">
                                    <a className="text-primaryDarkBlue">
                                      return to the dasboard
                                    </a>
                                  </Link>{" "}
                                  and do this later.
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </>
                    </div>
                  )}
                </main>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
}
