import React from "react";
import { Fragment } from "react";
import { supabase } from "@utils/supabaseClient";
import { Disclosure, Menu, Transition } from "@headlessui/react";
import { MenuIcon, BellIcon, XIcon } from "@heroicons/react/outline";
import Image from "next/image";
import Link from "next/link";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function Header() {
  return (
    <Disclosure as="nav" className="bg-primary">
      {({ open }) => (
        <>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0 pr-8">
                  <Image
                    className="block h-10 w-auto"
                    src="/images/bbw-logo-white.png"
                    alt="Books Beyond Words"
                    width={200}
                    height={86}
                  />
                </div>
                <div className="hidden md:block">
                  <div className="ml-10 flex items-baseline space-x-4">
                    <Link href="/">
                      <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
                        <div className="text-white font-semibold hover:text-gray-300 hover:bg-opacity-75">
                          User management
                        </div>
                      </div>
                    </Link>
                    <Link href="/stories">
                      <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
                        <div className="text-white font-semibold hover:text-gray-300 hover:bg-opacity-75">
                          Stories
                        </div>
                      </div>
                    </Link>
                    <Link href="/e-books">
                      <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
                        <div className="text-white font-semibold hover:text-gray-300 hover:bg-opacity-75">
                          e-books
                        </div>
                      </div>
                    </Link>
                    <Link href="/admin-users">
                      <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
                        <div className="text-white font-semibold hover:text-gray-300 hover:bg-opacity-75">
                          User admins
                        </div>
                      </div>
                    </Link>
                    {/* <Link href="/publish">
                      <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
                        <div className="text-white font-semibold hover:text-gray-300 hover:bg-opacity-75">
                          Publish
                        </div>
                      </div>
                    </Link> */}
                  </div>
                </div>
              </div>
              <div className="hidden md:block">
                <div className="ml-4 flex items-center md:ml-6">
                  {/* Profile dropdown */}
                  <Menu as="div" className="relative ml-3">
                    <div>
                      <Menu.Button className="flex max-w-xs items-center rounded-md bg-tertiary text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-1">
                        <span className="sr-only">Open user menu</span>
                        <div className="text-white p-2">Account</div>
                        {/* <img
                      className="h-8 w-8 rounded-full"
                      src={user.imageUrl}
                      alt=""
                    /> */}
                      </Menu.Button>
                    </div>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-100"
                      enterFrom="transform opacity-0 scale-95"
                      enterTo="transform opacity-100 scale-100"
                      leave="transition ease-in duration-75"
                      leaveFrom="transform opacity-100 scale-100"
                      leaveTo="transform opacity-0 scale-95"
                    >
                      <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <Menu.Item>
                          {({ active }) => (
                            <a
                              href="#"
                              onClick={() => supabase.auth.signOut()}
                              className={classNames(
                                active ? "bg-gray-100" : "",
                                "block px-4 py-2 text-sm text-gray-700"
                              )}
                            >
                              Log out
                            </a>
                          )}
                        </Menu.Item>
                      </Menu.Items>
                    </Transition>
                  </Menu>
                </div>
              </div>
              <div className="-mr-2 flex md:hidden">
                {/* Mobile menu button */}
                <Disclosure.Button className="inline-flex items-center justify-center rounded-md bg-primary p-2 text-indigo-200 hover:bg-indigo-500 hover:bg-opacity-75 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-indigo-600">
                  <span className="sr-only">Open main menu</span>
                  {open ? (
                    <XIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <MenuIcon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Disclosure.Button>
              </div>
            </div>
          </div>

          <Disclosure.Panel className="md:hidden">
            <Link href="/">
              <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
                <Disclosure.Button as="div">User managment</Disclosure.Button>
              </div>
            </Link>

            <Link href="#">
              <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
                <Disclosure.Button as="div">Screenings</Disclosure.Button>
              </div>
            </Link>
            <Link href="/admin-users">
              <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
                <Disclosure.Button as="div">User admins</Disclosure.Button>
              </div>
            </Link>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>

    //     <header className="bg-white shadow-sm">
    //     <div className="mx-auto max-w-7xl py-4 px-4 sm:px-6 lg:px-8">
    //       <h1 className="text-lg font-semibold leading-6 text-greyDark">
    //         Campaigns
    //       </h1>
    //     </div>
    //   </header>
  );
}
