import React from "react";
import { useState } from "react";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";

export default function NotAuthorised() {
  return (
    <div className="min-h-screen  bg-gray-300 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* <img
          className="mx-auto h-8 w-auto"
          src="/innatemotion-logo-horizontal.png"
          alt="In8Motion"
        /> */}
        <h2 className="font-Ubuntu mt-6 text-center text-2xl font-extrabold text-gray-900">
          Not authorised
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6">
        <div className="bg-white py-12 px-4 shadow sm:rounded-lg sm:px-10">
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Please contact your administrator
            </label>

            <div className="mt-0 flex flex-row space-x-2"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
